package com.zsmall.product.biz.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.lang.Console;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.BigExcelWriter;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.domain.model.LoginUser;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.core.utils.ServletUtils;
import com.hengjian.common.core.utils.SpringUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.redis.utils.RedisUtils;
import com.hengjian.common.satoken.holder.LoginContextLocalHolder;
import com.hengjian.common.satoken.holder.runnable.HeaderLocaleRunnable;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.extend.event.OSSUploadEvent;
import com.hengjian.extend.utils.SystemEventUtils;
import com.hengjian.system.domain.vo.SysOssVo;
import com.zsmall.common.constant.FileNameConstants;
import com.zsmall.common.constant.MallConstants;
import com.zsmall.common.constant.RedisConstants;
import com.zsmall.common.domain.LocaleMessage;
import com.zsmall.common.domain.dto.ChangeFieldDTO;
import com.zsmall.common.enums.BusinessCodeEnum;
import com.zsmall.common.enums.ExcelMessageEnum;
import com.zsmall.common.enums.common.AttachmentTypeEnum;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.common.GlobalStateEnum;
import com.zsmall.common.enums.downloadRecord.DownloadTypePlusEnum;
import com.zsmall.common.enums.downloadRecord.RecordStateEnum;
import com.zsmall.common.enums.order.OrderExceptionEnum;
import com.zsmall.common.enums.orderImportRecord.ImportStateEnum;
import com.zsmall.common.enums.priceLog.PriceOperateLog;
import com.zsmall.common.enums.product.*;
import com.zsmall.common.enums.productActivity.ProductActivityStateEnum;
import com.zsmall.common.enums.productMapping.SyncStateEnum;
import com.zsmall.common.enums.productSku.DropShippingStockAvailableEnum;
import com.zsmall.common.enums.productSku.FiledTypeEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.common.enums.warehouse.WarehouseTypeEnum;
import com.zsmall.common.exception.ExcelMessageException;
import com.zsmall.common.properties.FileProperties;
import com.zsmall.common.util.ExcelMsgBuilder;
import com.zsmall.common.util.ZExcelUtil;
import com.zsmall.extend.utils.ZSMallSystemEventUtils;
import com.zsmall.order.entity.domain.WholesaleIntentionOrderItem;
import com.zsmall.order.entity.iservice.IWholesaleIntentionOrderItemService;
import com.zsmall.product.biz.service.*;
import com.zsmall.product.biz.support.EsProductSupport;
import com.zsmall.product.biz.support.ProductSupport;
import com.zsmall.product.biz.support.ProductSkuDataSupper;
import com.zsmall.product.entity.domain.*;
import com.zsmall.common.enums.product.ProductReviewTypeEnum;
import com.zsmall.product.entity.domain.bo.UniversalBo.UniversalQueryBo;
import com.zsmall.product.entity.domain.bo.product.*;
import com.zsmall.product.entity.domain.bo.productSku.ProductSkuImportBo;
import com.zsmall.product.entity.domain.dto.product.ProductReviewDTO;
import com.zsmall.product.entity.domain.dto.productImport.ProductImportDTO;
import com.zsmall.product.entity.domain.dto.productSku.ProductSkuReviewDTO;
import com.zsmall.product.entity.domain.vo.ProductReviewRecordVo;
import com.zsmall.product.entity.domain.vo.ProductSkuAttachmentVo;
import com.zsmall.product.entity.domain.vo.product.*;
import com.zsmall.product.entity.domain.vo.productGlobalAttribute.ProductGlobalAttributeSimpleVo;
import com.zsmall.product.entity.domain.vo.productImport.ProductImportRecordVo;
import com.zsmall.product.entity.domain.vo.productSku.ProductSkuListVo;
import com.zsmall.product.entity.domain.vo.productSkuStock.ProductSkuStockSimpleVo;
import com.zsmall.product.entity.iservice.*;
import com.zsmall.product.entity.util.ProductCodeGenerator;
import com.zsmall.system.entity.domain.DownloadRecord;
import com.zsmall.system.entity.domain.SiteCountryCurrency;
import com.zsmall.system.entity.domain.TenantSalesChannel;
import com.zsmall.system.entity.iservice.IDownloadRecordService;
import com.zsmall.system.entity.iservice.ISiteCountryCurrencyService;
import com.zsmall.system.entity.iservice.ITenantSalesChannelService;
import com.zsmall.system.entity.util.DownloadRecordUtil;
import com.zsmall.system.entity.util.DownloadRecordV2Util;
import com.zsmall.warehouse.entity.domain.LogisticsTemplate;
import com.zsmall.warehouse.entity.domain.Warehouse;
import com.zsmall.warehouse.entity.domain.WarehouseAddress;
import com.zsmall.warehouse.entity.iservice.ILogisticsTemplateService;
import com.zsmall.warehouse.entity.iservice.IWarehouseAddressService;
import com.zsmall.warehouse.entity.iservice.IWarehouseService;
import lombok.Cleanup;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Sheet;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 商品SPUService业务层处理
 *
 * <AUTHOR> Li
 * @date 2023-05-26
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ProductServiceImpl implements ProductService {

    // 业务Service
    private final ProductSkuService productSkuService;
    private static final ExecutorService executor = Executors.newFixedThreadPool(2);
    private final ISiteCountryCurrencyService iSiteCountryCurrencyService;

    // 数据库Service
    private final IProductService iProductService;
    private final IProductMappingService iProductMappingService;
    private final IProductCategoryService iProductCategoryService;
    private final ProductReviewRecordService productReviewRecordService;
    private final IProductReviewRecordService iProductReviewRecordService;
    private final IProductReviewChangeDetailService iProductReviewChangeDetailService;
    private final IProductCategoryRelationService iProductCategoryRelationService;
    private final IProductAttributeService iProductAttributeService;
    private final IProductGlobalAttributeService iProductGlobalAttributeService;
    private final IProductAttachmentService iProductAttachmentService;
    private final IProductSkuService iProductSkuService;
//    private final IProductActivityService iProductActivityService;
//    private final IProductActivityItemService iProductActivityItemService;
    private final IProductSkuAttributeService iProductSkuAttributeService;
    private final IProductSkuAttachmentService iProductSkuAttachmentService;
    private final IProductSkuDetailService iProductSkuDetailService;
    private final IProductSkuPriceService iProductSkuPriceService;
    private final IProductSkuPriceLogService iProductSkuPriceLogService;
    private final IProductSkuStockService iProductSkuStockService;
    private final ProductSkuPriceRuleService iProductSkuPriceRuleService;
    private final IProductSkuPriceRuleRelationService iProductSkuPriceRuleRelationService;
    private final ILogisticsTemplateService iLogisticsTemplateService;
    private final IProductImportRecordService iProductImportRecordService;
    private final IWarehouseService iWarehouseService;
    private final IWarehouseAddressService iWarehouseAddressService;
    private final IDownloadRecordService iDownloadRecordService;
    private final ProductSupport productSupport;
    private final EsProductSupport esProductSupport;
    private final IWholesaleIntentionOrderItemService iWholesaleIntentionOrderItemService;
    private final IViewProductStockService iViewProductStockService;
    private final ITenantSalesChannelService tenantSalesChannelService;
    private final ProductCodeGenerator productCodeGenerator;
    private final FileProperties fileProperties;

    private static final ThreadPoolExecutor ioThreadPoolExecutor = SpringUtils.getBean("ioThreadPoolExecutor", ThreadPoolExecutor.class);
    /**
     * 查询商品SPU列表
     */
    @Override
    public ProductTableInfoVo queryPageList(ProductQueryBo bo, PageQuery pageQuery) {
        TenantType tenantTypeEnum = LoginHelper.getTenantTypeEnum();
        Long siteId = bo.getSiteId();
        String tenantId = LoginHelper.getTenantId();
        // 查询库存异常的商品
        List<String> productSkuCodeList;
        if(null != bo.getLockExceptionCode()){
            List<ProductSkuStock> productSkuStockList = iProductSkuStockService.queryByLockExceptionCode(bo.getLockExceptionCode());
            if(CollUtil.isNotEmpty(productSkuStockList)){
                productSkuCodeList = productSkuStockList.stream().map(ProductSkuStock::getProductSkuCode).distinct().collect(Collectors.toList());
                bo.setProductSkuCodeList(productSkuCodeList);
            }
        }
        bo.setProductType(ProductTypeEnum.NormalProduct.name());
        IPage<Product> productIPage = iProductService.queryPageList(pageQuery.build(), bo);

        List<SiteCountryCurrency> siteCountryCurrencies = iSiteCountryCurrencyService.list();
        List<Product> records = productIPage.getRecords();
        List<ProductListVo> results = new ArrayList<>();
        List<Long> productIds = records.stream().map(Product::getId).collect(Collectors.toList());
        List<String> productCodes = records.stream().map(Product::getProductCode).collect(Collectors.toList());
        List<ProductReviewRecord> reviewRecords =TenantHelper.ignore(()->iProductReviewRecordService.queryRecordByProductCodesAndReviewType(productCodes, ProductVerifyStateEnum.Rejected));
        if(CollUtil.isNotEmpty(reviewRecords)){
            List<Long> productReviewRecordIds = reviewRecords.stream().map(ProductReviewRecord::getId).collect(Collectors.toList());
            List<ProductReviewRecord> productReviewRecords = iProductReviewRecordService.listByIds(productReviewRecordIds);
            reviewRecords = productReviewRecords;
        }
        Map<String, ProductReviewRecord> reviewRecordMap = reviewRecords.stream()
                                                                      .collect(Collectors.toMap(ProductReviewRecord::getProductCode, Function.identity()));

        String skuShelfState = bo.getSkuShelfState();
        List<ProductSkuListVo> productSkuLists;
        if(StringUtils.isNotEmpty(skuShelfState)){
            productSkuLists = iProductSkuService.queryProductSkuListVoByProductIdsAndShelfState(productIds,skuShelfState);
        }else {
            UniversalQueryBo universalQueryBo = new UniversalQueryBo();
            universalQueryBo.setQueryType(bo.getQueryType());
            universalQueryBo.setQueryValue(bo.getQueryValue());
            universalQueryBo.setShelfState(bo.getShelfState());
            universalQueryBo.setProductType(bo.getProductType());
            universalQueryBo.setSiteId(bo.getSiteId());
            universalQueryBo.setAuditStatus(bo.getAuditStatus());
            universalQueryBo.setSkuShelfState(bo.getSkuShelfState());
            universalQueryBo.setProductSkuCodeList(bo.getProductSkuCodeList());

            productSkuLists = iProductSkuService.queryProductSkuListVoByProductIds(productIds, siteId, universalQueryBo);
        }
        stockAssignment(productSkuLists);
        if(CollUtil.isNotEmpty(productSkuLists)){
            for (ProductSkuListVo productSkuListVo : productSkuLists){
               if(StringUtils.isNotEmpty(productSkuListVo.getSpecComposeName())){
                   productSkuListVo.setSpecValName(transformString(productSkuListVo.getSpecComposeName()));
               }
            }
        }

        List<ProductSkuAttachmentVo> productSkuAttachmentVos = TenantHelper.ignore(()->iProductSkuAttachmentService.queryFirstImageByProductIds(productIds,tenantId));
        Map<Long, ProductSkuAttachmentVo> productSkuAttachmentMap = productSkuAttachmentVos.stream()
                                                                                           .collect(Collectors.toMap(ProductSkuAttachmentVo::getProductId, Function.identity()));
        Map<Long, List<ProductSkuListVo>> productSkuMap = productSkuLists.stream()
                                                                         .collect(Collectors.groupingBy(ProductSkuListVo::getProductId));
//        productSkuLists转换成map,key为productId,value 为 List<ProductSkuListVo>

        // 取出所有的 重新匹配  key = productId value = List<ProductSkuListVo> productSkuList 看下这个sql能不能优化有些不需要的字段就不要了
        for (Product product : records) {
            String name = product.getName();
            String productCode = product.getProductCode();
            ShelfStateEnum shelfState = product.getShelfState();
            ProductVerifyStateEnum verifyState = product.getVerifyState();
            SupportedLogisticsEnum supportedLogistics = product.getSupportedLogistics();

            Long productId = product.getId();

            List<ProductSkuListVo> productSkuList = productSkuMap.get(productId);

            if (CollUtil.isEmpty(productSkuList)) {
                // 存在单sku被删除的场景
                continue;
            }

            ProductSkuAttachmentVo productSkuAttachmentVo = productSkuAttachmentMap.get(productId);
            ProductListVo productListVo = new ProductListVo();
            productListVo.setProductName(name);
            productListVo.setProductCode(productCode);
            productListVo.setProductType(product.getProductType().name());
            productListVo.setVerifyState(verifyState.name());
            productListVo.setShelfState(shelfState.name());
            productListVo.setInventoryPushTime(product.getInventoryPushTime());
            productListVo.setSupportedLogistics(ObjectUtil.isNotNull(supportedLogistics) ? supportedLogistics.name() : null);

            if (ProductVerifyStateEnum.Rejected.equals(verifyState)) {
                ProductReviewRecord reviewRecord = reviewRecordMap.get(productCode);
                if (reviewRecord != null) {
                    productListVo.setReviewOpinion(reviewRecord.getReviewOpinion());
                    productListVo.setReviewOpinionOption(reviewRecord.getReviewOpinionOption());
                }
            }

            if (productSkuAttachmentVo != null) {
                String attachmentShowUrl = productSkuAttachmentVo.getAttachmentShowUrl();
                productListVo.setImageShowUrl(attachmentShowUrl);
            }

            for (ProductSkuListVo skuListVo : productSkuList) {
                // 根据站点再套内循环
                List<ProductSitePriceBo> sitePriceBosNew = new ArrayList<>();
                List<ProductSitePriceBo> sitePriceBos = skuListVo.getSitePriceBos();
                // sitePriceBos转换map siteId为key,value为ProductSitePriceBo
                Map<Long, ProductSitePriceBo> sitePriceBosMap = sitePriceBos.stream()
                                                                    .collect(Collectors.toMap(ProductSitePriceBo::getSiteId, Function.identity()));
                // todo 详情那边做了,要把这里的无效循环去掉

                if(ObjectUtil.isNotEmpty(siteId)){
                    ProductSitePriceBo productSitePriceBo;
                    // 如果有价格相关就放入,id为null的创建空
                    productSitePriceBo = sitePriceBosMap.get(siteId);
                    if(ObjectUtil.isNotEmpty(productSitePriceBo)){
                        sitePriceBosNew.add(productSitePriceBo);
                    }
                }else {
                    for (SiteCountryCurrency siteCountryCurrency : siteCountryCurrencies) {

                        ProductSitePriceBo productSitePriceBo;
                        // 如果有价格相关就放入,id为null的创建空
                        productSitePriceBo = sitePriceBosMap.get(siteCountryCurrency.getId());
                        if(ObjectUtil.isNotEmpty(productSitePriceBo)){
                            sitePriceBosNew.add(productSitePriceBo);
                        }

                    }
                }

                skuListVo.setSitePriceBos(sitePriceBosNew);
            }

            productListVo.setSkuList(productSkuList);
            results.add(productListVo);
        }

        ProductTableInfoVo vo = BeanUtil.toBean(TableDataInfo.build(results, productIPage.getTotal()), ProductTableInfoVo.class);
        if (TenantType.Supplier.equals(tenantTypeEnum)) {
            Boolean autoOnShelf = ZSMallSystemEventUtils.checkAutoOnShelfEvent(LoginHelper.getTenantId());
            vo.setAutoOnShelf(autoOnShelf);
        }

        return vo;
    }

    /**
     * 查询商品完整信息
     *
     * @param bo
     * @return
     */
    @Override
    public ProductIntactInfoVo queryIntactInfo(ProductBo bo) throws RStatusCodeException {
        String productCode = bo.getProductCode();
        if (StrUtil.isBlank(productCode)) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }
        String tenantId = LoginHelper.getTenantId();
        Product product = iProductService.queryByProductCodeNotDelete(productCode);
        Map<Long, SiteCountryCurrency> siteIdMap = iSiteCountryCurrencyService.getSiteIdMap();
        if (product != null) {
            List<SiteCountryCurrency> siteCountryCurrencies = iSiteCountryCurrencyService.list();
            ProductIntactInfoVo productIntactInfoVo = new ProductIntactInfoVo();
            Long productId = product.getId();
            Long belongCategoryId = product.getBelongCategoryId();
            Date firstOnShelfTime = product.getFirstOnShelfTime();
            ProductVerifyStateEnum verifyState = product.getVerifyState();

            productIntactInfoVo.setId(productId);
            productIntactInfoVo.setProductCode(product.getProductCode());
            productIntactInfoVo.setProductName(product.getName());

            // 仓库Map，需要传递给前端供下拉选使用
            Map<String, List<WarehouseTempVo>> warehouseMap = queryWarehouseMapNoRedis();

            List<ProductCategory> productCategories = iProductCategoryService.queryCategoryChainById(belongCategoryId);
            List<Long> categoryIdList = CollUtil.newArrayList(-1L);
            categoryIdList.addAll(CollUtil.reverse(productCategories).stream().map(ProductCategory::getId).collect(Collectors.toList()));

            productIntactInfoVo.setBelongCategoryId(belongCategoryId);
            productIntactInfoVo.setCategoryIdList(categoryIdList);
            productIntactInfoVo.setSupportedLogistics(product.getSupportedLogistics().getValue());
            productIntactInfoVo.setForbiddenChannel(product.getForbiddenChannel());
            productIntactInfoVo.setDescription(product.getDescription());
            productIntactInfoVo.setAlreadyOnShelf(firstOnShelfTime != null);

            productIntactInfoVo.setVerifyState(verifyState.getValue());
            if (ProductVerifyStateEnum.Rejected.equals(verifyState)) {
                ProductReviewRecord reviewRecord = iProductReviewRecordService.queryRecordByProductCodeAndReviewType(productCode, ProductVerifyStateEnum.Rejected);
                if (reviewRecord != null) {
                    productIntactInfoVo.setReviewOpinion(reviewRecord.getReviewOpinion());
                    productIntactInfoVo.setReviewOpinionOption(reviewRecord.getReviewOpinionOption());
                }
            }

            // 处理附件
            List<ProductAttachment> otherAttachmentList = iProductAttachmentService.queryByProductIdAndType(productId, AttachmentTypeEnum.File);
            if (CollUtil.isNotEmpty(otherAttachmentList)) {
                ProductAttachment otherAttachment = otherAttachmentList.get(0);
                ProductIntactInfoVo.Attachment otherAttachmentBo = BeanUtil.toBean(otherAttachment, ProductIntactInfoVo.Attachment.class);
                productIntactInfoVo.setOtherAttachment(otherAttachmentBo);
            }

            // 通用规格
            List<ProductAttribute> genericSpecList = iProductAttributeService.queryByProductIdAndAttributeType(productId, AttributeTypeEnum.GenericSpec);
            List<ProductIntactInfoVo.GenericSpecList> genericSpecListBo = new ArrayList<>();
            if (CollUtil.isNotEmpty(genericSpecList)) {
                for (ProductAttribute productAttribute : genericSpecList) {
                    ProductIntactInfoVo.GenericSpecList new_vo = new ProductIntactInfoVo.GenericSpecList();
                    Long attributeSourceId = productAttribute.getAttributeSourceId();
                    ProductGlobalAttribute productGlobalAttribute = iProductGlobalAttributeService.queryByIdNotTenant(attributeSourceId);

                    new_vo.setId(productAttribute.getId());
                    new_vo.setKey(productAttribute.getAttributeName());
                    new_vo.setValue(productAttribute.getAttributeValue());
                    new_vo.setSourceId(productAttribute.getAttributeSourceId());
                    if (productGlobalAttribute != null) {
                        new_vo.setIsRequired(productGlobalAttribute.getIsRequired());
                        new_vo.setIsSupportCustom(productGlobalAttribute.getIsSupportCustom());
                    } else {
                        new_vo.setIsRequired(false);
                        new_vo.setIsSupportCustom(true);
                    }
                    genericSpecListBo.add(new_vo);
                }
            }

            // 可选规格
            List<ProductAttribute> optionalSpecList = iProductAttributeService.queryByProductIdAndAttributeType(productId, AttributeTypeEnum.OptionalSpec);
            List<ProductIntactInfoVo.OptionalSpecList> optionalSpecListBo = new ArrayList<>();
            if (CollUtil.isNotEmpty(optionalSpecList)) {
                for (ProductAttribute productAttribute : optionalSpecList) {
                    ProductIntactInfoVo.OptionalSpecList new_vo = new ProductIntactInfoVo.OptionalSpecList();
                    Long attributeSourceId = productAttribute.getAttributeSourceId();
                    ProductGlobalAttribute productGlobalAttribute = iProductGlobalAttributeService.queryByIdNotTenant(attributeSourceId);

                    new_vo.setId(productAttribute.getId());
                    new_vo.setKey(productAttribute.getAttributeName());
                    new_vo.setValues(productAttribute.getAttributeValues());
                    new_vo.setSourceId(attributeSourceId);
                    if (productGlobalAttribute != null) {
                        new_vo.setIsSupportCustom(productGlobalAttribute.getIsSupportCustom());
                    } else {
                        new_vo.setIsSupportCustom(true);
                    }
                    optionalSpecListBo.add(new_vo);
                }
            }

            // 商品特色
            List<ProductAttribute> featureList = iProductAttributeService.queryByProductIdAndAttributeType(productId, AttributeTypeEnum.Feature);
            List<ProductIntactInfoVo.ProductFeatureList> featureListBo = new ArrayList<>();
            if (CollUtil.isNotEmpty(featureList)) {
                for (ProductAttribute productAttribute : featureList) {
                    ProductIntactInfoVo.ProductFeatureList new_vo = new ProductIntactInfoVo.ProductFeatureList();
                    new_vo.setId(productAttribute.getId());
                    new_vo.setKey(productAttribute.getAttributeName());
                    new_vo.setValue(productAttribute.getAttributeValue());
                    featureListBo.add(new_vo);
                }
            }

            productIntactInfoVo.setGenericSpecList(genericSpecListBo);
            productIntactInfoVo.setOptionalSpecList(optionalSpecListBo);
            productIntactInfoVo.setProductFeatureList(featureListBo);

            // 处理SKU - 性能优化：使用批量查询替代N+1查询
            List<ProductSku> productSkuList = iProductSkuService.queryByProductIdNotDelete(productId);
            List<ProductIntactInfoVo.ProductSkuList> productSkuListVo = new ArrayList<>();
            // 全局价格审核标志
            Boolean globalPriceChange = false;
            if (CollUtil.isNotEmpty(productSkuList)) {
                long startTime = System.currentTimeMillis();
                log.info("【性能优化】开始处理{}个SKU的数据查询", productSkuList.size());

                // 批量查询所有关联数据
                List<Long> productSkuIds = ProductSkuDataSupper.extractSkuIds(productSkuList);
                List<String> productSkuCodes = ProductSkuDataSupper.extractSkuCodes(productSkuList);

                // 批量查询SKU详情
                List<ProductSkuDetail> allSkuDetails = iProductSkuDetailService.queryByProductSkuIds(productSkuIds);
                Map<Long, ProductSkuDetail> skuDetailMap = ProductSkuDataSupper.buildSkuDetailMap(allSkuDetails);

                // 批量查询SKU价格
                List<ProductSkuPrice> allSkuPrices = iProductSkuPriceService.queryByProductSkuIds(productSkuIds);
                Map<Long, List<ProductSkuPrice>> skuPriceMap = ProductSkuDataSupper.buildSkuPriceMap(allSkuPrices);

                // 批量查询SKU属性
                List<ProductSkuAttribute> allSkuAttributes = iProductSkuAttributeService.queryByProductSkuIds(productSkuIds);
                Map<Long, List<ProductSkuAttribute>> skuAttributeMap = ProductSkuDataSupper.buildSkuAttributeMap(allSkuAttributes);

                // 批量查询SKU图片附件
                List<ProductSkuAttachment> allImageAttachments = iProductSkuAttachmentService.queryByProductSkuIdsAndType(productSkuIds, AttachmentTypeEnum.Image);
                Map<Long, List<ProductSkuAttachment>> imageAttachmentMap = ProductSkuDataSupper.buildSkuAttachmentMap(allImageAttachments);

                // 批量查询SKU视频附件
                List<ProductSkuAttachment> allVideoAttachments = iProductSkuAttachmentService.queryByProductSkuIdsAndType(productSkuIds, AttachmentTypeEnum.Video);
                Map<Long, List<ProductSkuAttachment>> videoAttachmentMap = ProductSkuDataSupper.buildSkuAttachmentMap(allVideoAttachments);

                // 批量查询SKU库存
                List<ProductSkuStockSimpleVo> allSkuStocks = iProductSkuStockService.querySimpleVoByProductSkuCodes(productSkuCodes);
                Map<String, List<ProductSkuStockSimpleVo>> skuStockMap = ProductSkuDataSupper.buildSkuStockMap(allSkuStocks);

                // 收集所有需要查询物流模板的仓库编码
                Set<String> allWarehouseCodes = new HashSet<>();
                for (ProductSkuStockSimpleVo stock : allSkuStocks) {
                    if (ObjectUtil.isNotEmpty(stock.getWarehouseSystemCode())) {
                        allWarehouseCodes.add(stock.getWarehouseSystemCode());
                    }
                }

                // 使用真正的批量查询获取物流模板（一次查询，精确分组）
                Map<String, List<LogisticsTemplate>> logisticsTemplateMap = new HashMap<>();
                if (CollUtil.isNotEmpty(allWarehouseCodes)) {
                    List<String> warehouseCodeList = new ArrayList<>(allWarehouseCodes);

                    // 使用真正的批量查询：一次IN查询获取所有仓库的物流模板，并按仓库分组
                    logisticsTemplateMap = iLogisticsTemplateService.queryByWarehousesMap(warehouseCodeList);

                    log.info("【批量查询物流模板】使用IN查询{}个仓库，获得{}个仓库的物流模板",
                            warehouseCodeList.size(), logisticsTemplateMap.size());
                }

                // 批量查询SKU的价格审核记录存在性
                Map<String, Boolean> priceReviewExistsMap = iProductReviewRecordService.existsByProductSkuCodes(
                        productSkuCodes, ProductReviewTypeEnum.Price, ProductVerifyStateEnum.Pending);

                ProductSkuDataSupper.logPerformance("批量查询SKU关联数据", productSkuList.size(), startTime);

                // 获取该供应商的全部仓库信息（只查询一次）
                LambdaQueryWrapper<Warehouse> lqw = Wrappers.lambdaQuery();
                lqw.eq(Warehouse::getWarehouseState, GlobalStateEnum.Valid).eq(Warehouse::getTenantId, tenantId);
                List<Warehouse> warehouses = iWarehouseService.list(lqw);

                for (ProductSku productSku : productSkuList) {
                    Long productSkuId = productSku.getId();
                    ProductVerifyStateEnum skuVerifyState = productSku.getVerifyState();
                    String productSkuCode = productSku.getProductSkuCode();
                    ProductIntactInfoVo.ProductSkuList newVo = BeanUtil.toBean(productSku, ProductIntactInfoVo.ProductSkuList.class);

                    // 从Map中获取SKU详情（替代单个查询）
                    ProductSkuDetail productSkuDetail = ProductSkuDataSupper.getSkuDetail(skuDetailMap, productSkuId);
                    if (productSkuDetail != null) {
                        BeanUtil.copyProperties(productSkuDetail, newVo, "id");
                    }

                    // 从Map中获取SKU价格（替代单个查询）
                    List<ProductSkuPrice> productSkuPrices = ProductSkuDataSupper.getSkuPrices(skuPriceMap, productSkuId);
//                    BigDecimal msrp = productSkuPrice.getMsrp();
//                    BigDecimal originalUnitPrice = productSkuPrice.getOriginalUnitPrice();
//                    BigDecimal originalOperationFee = productSkuPrice.getOriginalOperationFee();
//                    BigDecimal originalFinalDeliveryFee = productSkuPrice.getOriginalFinalDeliveryFee();
//                    newVo.setMsrp(msrp);
//                    newVo.setUnitPrice(originalUnitPrice);
//                    newVo.setOperationFee(originalOperationFee);
//                    newVo.setFinalDeliveryFee(originalFinalDeliveryFee);

                    // 审核通过的商品才能正常展示上下架的状态
                    if (ProductVerifyStateEnum.Accepted.equals(skuVerifyState)) {
                        newVo.setIsOnShelf(ObjectUtil.equal(productSku.getShelfState(), ShelfStateEnum.OnShelf));
                    } else if (ProductVerifyStateEnum.Pending.equals(skuVerifyState)) {
                        newVo.setIsOnShelf(false);
                    } else {  // 未审核通过的，isOnShelf都等于true，前端就可以正常编辑SKU信息
                        newVo.setIsOnShelf(true);
                    }

                    newVo.setVerifyState(skuVerifyState.name());
                    // SPU审核通过，但是SKU不通过，需要单独显示SKU被拒绝的原因
                    if (ProductVerifyStateEnum.Accepted.equals(verifyState) && ProductVerifyStateEnum.Rejected.equals(skuVerifyState)) {
                        ProductReviewRecordVo reviewRecordVo = iProductReviewRecordService
                            .queryNewestRecordByProductSkuCode(productSkuCode, ProductReviewTypeEnum.NewProductSku, ProductVerifyStateEnum.Rejected);
                        if (reviewRecordVo != null) {
                            newVo.setReviewOpinion(reviewRecordVo.getReviewOpinion());
                            newVo.setReviewOpinionOption(reviewRecordVo.getReviewOpinionOption());
                        }
                    }

                    // 从批量查询结果中获取价格审核记录存在性（替代单个查询）
                    Boolean exists = ProductSkuDataSupper.getReviewExists(priceReviewExistsMap, productSkuCode);
                    if (!globalPriceChange && exists == true) {
                        globalPriceChange = true;
                    }
                    newVo.setHasPriceChange(exists);

                    // 从Map中获取SKU属性（替代单个查询）
                    List<ProductSkuAttribute> skuAttributes = ProductSkuDataSupper.getSkuAttributes(skuAttributeMap, productSkuId);
                    List<ProductIntactInfoVo.SpecComposeList> specComposeList = new ArrayList<>();
                    for (ProductSkuAttribute skuAttribute : skuAttributes) {
                        ProductIntactInfoVo.SpecComposeList specComposeBo = new ProductIntactInfoVo.SpecComposeList();
                        specComposeBo.setId(skuAttribute.getId());
                        specComposeBo.setKey(skuAttribute.getAttributeName());
                        specComposeBo.setValue(skuAttribute.getAttributeValue());
                        specComposeBo.setSourceId(skuAttribute.getAttributeSourceId());
                        specComposeList.add(specComposeBo);
                    }
                    List<ProductSitePriceBo> sitePriceBosNew = new ArrayList<>();
                    Map<Long, ProductSkuPrice> sitePriceMap = productSkuPrices.stream()
                                                                         .collect(Collectors.toMap(ProductSkuPrice::getSiteId, Function.identity()));
                    for (SiteCountryCurrency siteCountryCurrency : siteCountryCurrencies) {

                        ProductSitePriceBo productSitePriceBo = new ProductSitePriceBo();
                        // 如果有价格相关就放入,id为null的创建空
                        ProductSkuPrice price = sitePriceMap.get(siteCountryCurrency.getId());
                        if(ObjectUtil.isEmpty(price)||ObjectUtil.isEmpty(price.getId())){
                            productSitePriceBo.setSiteId(siteCountryCurrency.getId());
                            productSitePriceBo.setCurrencySymbol(siteCountryCurrency.getCurrencySymbol());
                            productSitePriceBo.setCountryCode(siteCountryCurrency.getCountryCode());
                            productSitePriceBo.setCurrencyCode(siteCountryCurrency.getCurrencyCode());
                        }
                        if(ObjectUtil.isNotEmpty(price)){

                            productSitePriceBo.setId(price.getId());
                            productSitePriceBo.setSiteId(price.getSiteId());
                            productSitePriceBo.setCurrencySymbol(siteCountryCurrency.getCurrencySymbol());
                            productSitePriceBo.setCountryCode(siteCountryCurrency.getCountryCode());
                            productSitePriceBo.setCurrencyCode(siteCountryCurrency.getCurrencyCode());
                            productSitePriceBo.setDropShippingPrice(price.getPlatformDropShippingPrice());
                            productSitePriceBo.setPickUpPrice(price.getPlatformPickUpPrice());
                            productSitePriceBo.setUnitPrice(price.getPlatformUnitPrice());
                            productSitePriceBo.setOperationFee(price.getPlatformOperationFee());
                            productSitePriceBo.setFinalDeliveryFee(price.getPlatformFinalDeliveryFee());

                        }
                        if(ObjectUtil.isNotEmpty(productSitePriceBo)){
                            sitePriceBosNew.add(productSitePriceBo);
                        }

                    }
                    newVo.setSitePriceBos(sitePriceBosNew);

                    // 从Map中获取SKU图片附件（替代单个查询）
                    List<ProductSkuAttachment> imageVoList = ProductSkuDataSupper.getSkuAttachments(imageAttachmentMap, productSkuId);
                    List<ProductIntactInfoVo.Attachment> imageList = BeanUtil.copyToList(imageVoList, ProductIntactInfoVo.Attachment.class);

                    // 从Map中获取SKU视频附件（替代单个查询）
                    List<ProductSkuAttachment> videoVoList = ProductSkuDataSupper.getSkuAttachments(videoAttachmentMap, productSkuId);
                    List<ProductIntactInfoVo.Attachment> videoList = BeanUtil.copyToList(videoVoList, ProductIntactInfoVo.Attachment.class);

                    // 从Map中获取SKU库存（替代单个查询）
                    List<ProductSkuStockSimpleVo> productSkuStockVo = ProductSkuDataSupper.getSkuStocks(skuStockMap, productSkuCode);
                    List<ProductIntactInfoVo.StockConfigList> stockConfigList = BeanUtil.copyToList(productSkuStockVo, ProductIntactInfoVo.StockConfigList.class);

                    // 处理库存配置的物流模板和代发库存设置（使用批量查询结果）
                    for (ProductIntactInfoVo.StockConfigList configList : stockConfigList) {
                        String warehouseSystemCode = configList.getWarehouseSystemCode();

                        // 从批量查询结果中获取物流模板（不再执行单个查询）
                        List<LogisticsTemplate> templateList = logisticsTemplateMap.getOrDefault(warehouseSystemCode, new ArrayList<>());

                        if (CollUtil.isNotEmpty(templateList)) {
                            List<ProductIntactInfoVo.LogisticsTemplateSelectVo> templateSelectVoList = BeanUtil.copyToList(templateList, ProductIntactInfoVo.LogisticsTemplateSelectVo.class);
                            configList.setLogisticsTemplateSelect(templateSelectVoList);
                        }
                        // 设置一件代发库存
                        if(null != configList.getDropShippingStockAvailable() && configList.getDropShippingStockAvailable() == DropShippingStockAvailableEnum.NOT_SINGLE.getCode()){
                            configList.setProxyStockQuantity(configList.getQuantity());
                        }else {
                            configList.setProxyStockQuantity(0);
                        }
                    }
                    // 填充仓库
                    if(CollUtil.isNotEmpty(warehouses)){
                        if(CollUtil.isNotEmpty(stockConfigList)){
                            List<Warehouse> sameWarehouse = new ArrayList<>();
                            for(ProductIntactInfoVo.StockConfigList configList : stockConfigList){
                                for(Warehouse warehouse : warehouses){
                                    if(configList.getWarehouseSystemCode().equals(warehouse.getWarehouseSystemCode())){
                                        sameWarehouse.add(warehouse);
                                        break;
                                    }
                                }
                            }
                            if(CollUtil.isNotEmpty(sameWarehouse)){
                                warehouses.removeAll(sameWarehouse);
                            }
                        }
                        for(Warehouse warehouse : warehouses){
                            ProductIntactInfoVo.StockConfigList stockConfigListAll = new ProductIntactInfoVo.StockConfigList();
                            stockConfigListAll.setWarehouseName(warehouse.getWarehouseName());
                            stockConfigListAll.setWarehouseSystemCode(warehouse.getWarehouseSystemCode());
                            stockConfigList.add(stockConfigListAll);
                        }
                    }

                    newVo.setSpecComposeList(specComposeList);
                    newVo.setImageList(imageList);
                    newVo.setVideoList(videoList);
                    newVo.setStockConfigList(stockConfigList);

                    newVo.setWarehouseList(warehouseMap.get(WarehouseTypeEnum.BizArk.getValue()));
                    newVo.setWarehouseListTemp(warehouseMap.get(WarehouseTypeEnum.BizArk.getValue()));

                    productSkuListVo.add(newVo);
                }
            }
            productIntactInfoVo.setGlobalPriceChange(globalPriceChange);
            productIntactInfoVo.setProductSkuList(productSkuListVo);
            return productIntactInfoVo;
        } else {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.PRODUCT_NOT_EXIST);
        }
    }
    /**
     * 新增商品（完整信息）
     */
    @Override
    @Transactional(rollbackFor = {Exception.class, RStatusCodeException.class})
    public ProductSaveVo insertByIntactInfo(ProductIntactInfoBo bo) throws RStatusCodeException {
        LoginUser user = LoginHelper.getLoginUser();
        String tenantType = user.getTenantType();
        String tenantId = null;
        if(TenantType.Supplier.name().equals(tenantType)||TenantType.Manager.name().equals(tenantType)){
            tenantId = user.getTenantId();
        }
//        String tenantId = "SJN1857";

        String productName = bo.getProductName();
        Long belongCategoryId = bo.getBelongCategoryId();
        String supportedLogistics = bo.getSupportedLogistics();
        String description = bo.getDescription();
        JSONArray forbiddenChannel = bo.getForbiddenChannel();

        String deliveryTime = bo.getDeliveryTime();
        String deliverGoodsTime = bo.getDeliverGoodsTime();


        if (StrUtil.hasEmpty(productName, supportedLogistics) || belongCategoryId == null) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }

        if (CollUtil.isEmpty(forbiddenChannel)) {
            forbiddenChannel = new JSONArray();
        }

        Product product = new Product();
        String productCode = productCodeGenerator.codeGenerate(BusinessCodeEnum.ProductCode);
        SupportedLogisticsEnum supportedLogisticsEnum = SupportedLogisticsEnum.valueOf(supportedLogistics);

        product.setDeliveryTime(deliveryTime);
        product.setDeliverGoodsTime(deliverGoodsTime);
        product.setName(productName);
        product.setBelongCategoryId(belongCategoryId);
        product.setSupportedLogistics(supportedLogisticsEnum);
        product.setDescription(StrUtil.emptyToNull(description));
        product.setProductCode(productCode);
        product.setProductType(ProductTypeEnum.NormalProduct);
        product.setForbiddenChannel(forbiddenChannel);
        product.setShelfState(ShelfStateEnum.ForcedOffShelf);
        product.setVerifyState(ProductVerifyStateEnum.Draft);
        product.setDownloadCount(0);
        iProductService.save(product);

        Long productId = product.getId();

        List<ProductIntactInfoBo.GenericSpecList> genericSpecList = bo.getGenericSpecList();
        List<ProductIntactInfoBo.OptionalSpecList> optionalSpecList = bo.getOptionalSpecList();
        List<ProductIntactInfoBo.ProductFeatureList> productFeatureList = bo.getProductFeatureList();
        List<ProductIntactInfoBo.ProductSkuList> productSkuList = bo.getProductSkuList();

        if (CollUtil.isEmpty(productSkuList)) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.SKU_INFORMATION_REQUIRE);
        }

        ProductAttachmentBo otherAttachment = bo.getOtherAttachment();

        List<ProductAttribute> productAttributeList = new ArrayList<>();

        /* 处理通用规格 */
        if (CollUtil.isNotEmpty(genericSpecList)) {
            for (int i = 0; i < genericSpecList.size(); i++) {
                ProductIntactInfoBo.GenericSpecList genericSpec = genericSpecList.get(i);
                String key = genericSpec.getKey();
                String value = genericSpec.getValue();
                if (StrUtil.isBlank(value)) {
                    continue;
                }

                Long sourceId = genericSpec.getSourceId();
                productAttributeList.add(productSupport.setProductAttribute(null, productId, key, value, AttributeTypeEnum.GenericSpec, sourceId, i));
            }
        }

        /* 处理可选规格 */
        if (CollUtil.isNotEmpty(optionalSpecList)) {
            for (int i = 0; i < optionalSpecList.size(); i++) {
                ProductIntactInfoBo.OptionalSpecList optionalSpec = optionalSpecList.get(i);
                Long sourceId = optionalSpec.getSourceId();
                String key = optionalSpec.getKey();
                JSONArray values = optionalSpec.getValues();
                productAttributeList.add(productSupport.setProductAttribute(null, productId, key, values, AttributeTypeEnum.OptionalSpec, sourceId, i));
            }
        }

        /* 处理商品特色 */
        if (CollUtil.isNotEmpty(productFeatureList)) {
            for (int i = 0; i < productFeatureList.size(); i++) {
                ProductIntactInfoBo.ProductFeatureList feature = productFeatureList.get(i);
                String key = feature.getKey();
                String value = feature.getValue();
                if (StrUtil.isBlank(value)) {
                    continue;
                }

                productAttributeList.add(productSupport.setProductAttribute(null, productId, key, value, AttributeTypeEnum.Feature, null, i));
            }
        }
        iProductAttributeService.insertOrUpdateBatch(productAttributeList);

        // 处理其他附件
        ProductAttachment productAttachment = attachmentConvertEntity(otherAttachment, ProductAttachment.class);
        if (productAttachment != null) {
            productAttachment.setProductId(productId);
            productAttachment.setAttachmentType(AttachmentTypeEnum.File);
            iProductAttachmentService.insert(productAttachment);
        }

        // 处理分类关系
        List<ProductCategory> productCategoryList = iProductCategoryService.queryCategoryChainById(belongCategoryId);
        List<ProductCategoryRelation> categoryRelationList = new ArrayList<>();
        if (CollUtil.isNotEmpty(productCategoryList)) {
            for (ProductCategory productCategory : productCategoryList) {
                ProductCategoryRelation pcr = new ProductCategoryRelation();
                pcr.setProductId(productId);
                pcr.setProductCategoryId(productCategory.getId());
                categoryRelationList.add(pcr);
            }
        }
        iProductCategoryRelationService.insertBatch(categoryRelationList);
        LambdaQueryWrapper<SiteCountryCurrency> wrapper = new LambdaQueryWrapper<SiteCountryCurrency>().eq(SiteCountryCurrency::getDelFlag, 0);
        List<SiteCountryCurrency> siteCountryCurrencies = iSiteCountryCurrencyService.list(wrapper);
        //siteCountryCurrencies 转换成 key是id,value是元素的map
        Map<Long, SiteCountryCurrency> siteCountryCurrencyMap = siteCountryCurrencies.stream()
                                                                                     .collect(Collectors.toMap(SiteCountryCurrency::getId, Function.identity()));
        // 处理SKU
        for (int sort = 0; sort < productSkuList.size(); sort++) {
            ProductIntactInfoBo.ProductSkuList req_productSku = productSkuList.get(sort);
            String sku = req_productSku.getSku();
            String erpSku = req_productSku.getErpSku();
            String upc = req_productSku.getUpc();
            List<ProductAttachmentBo> imageList = req_productSku.getImageList();
            List<ProductIntactInfoBo.SpecComposeList> specComposeList = req_productSku.getSpecComposeList();
            List<ProductIntactInfoBo.StockConfigList> stockConfigList = req_productSku.getStockConfigList();

            boolean existSku = iProductSkuService.existSku(sku, tenantId, null);
            if (existSku) {
                throw new RStatusCodeException(ZSMallStatusCodeEnum.PRODUCT_SKU_CODE_REPEAT.args(sku));
            }

            if (StrUtil.isNotBlank(upc)) {
                boolean existUpc = iProductSkuService.existUpc(upc, null);
                if (existUpc) {
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.PRODUCT_UPC_CODE_REPEAT.args(upc));
                }
            } else {
                upc = null;
            }

            if (CollUtil.isEmpty(imageList)) {
                throw new RStatusCodeException(ZSMallStatusCodeEnum.SKU_REQUIRES_ONE_IMAGE);
            }
            if (CollUtil.isEmpty(stockConfigList)) {
                throw new RStatusCodeException(ZSMallStatusCodeEnum.WAREHOUSE_NOT_SELECTED);
            }
            if (CollUtil.isEmpty(specComposeList)) {
                throw new RStatusCodeException(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
            }

            String stockManager = req_productSku.getStockManager();
            // 暂时修改为恒健仓库
            StockManagerEnum stockManagerEnum = StockManagerEnum.BizArk;
            if(StringUtils.isNotEmpty(stockManager)){
                stockManagerEnum = StockManagerEnum.valueOf(stockManager);
            }


            BigDecimal unitPrice = req_productSku.getUnitPrice();
            BigDecimal operationFee = req_productSku.getOperationFee();
            //调整尾程派送费为0
            BigDecimal finalDeliveryFee = req_productSku.getFinalDeliveryFee();
            //BigDecimal finalDeliveryFee = BigDecimal.ZERO;
            BigDecimal msrp = req_productSku.getMsrp();
            String length = req_productSku.getLength();
            String width = req_productSku.getWidth();
            String height = req_productSku.getHeight();
            String lengthUnit = req_productSku.getLengthUnit();
            String weight = req_productSku.getWeight();
            String weightUnit = req_productSku.getWeightUnit();
            String packLength = req_productSku.getPackLength();
            String packWidth = req_productSku.getPackWidth();
            String packHeight = req_productSku.getPackHeight();
            String packLengthUnit = req_productSku.getPackLengthUnit();
            String packWeight = req_productSku.getPackWeight();
            String packWeightUnit = req_productSku.getPackWeightUnit();
            boolean samePacking = req_productSku.isSamePacking();

            String transportMethod = req_productSku.getTransportMethod();
            String specComposeName = req_productSku.getSpecComposeName();
            String specValName = req_productSku.getSpecValName();

            List<ProductAttachmentBo> videoList = req_productSku.getVideoList();

            String productSkuCode = productCodeGenerator.codeGenerate(BusinessCodeEnum.ProductSkuCode);

            if (SupportedLogisticsEnum.PickUpOnly.equals(supportedLogisticsEnum) && finalDeliveryFee == null) {
                finalDeliveryFee = BigDecimal.ZERO;
            }

//            if (ObjectUtil.hasEmpty(unitPrice, operationFee, finalDeliveryFee)) {
//                throw new RStatusCodeException(ZSMallStatusCodeEnum.SKU_INFORMATION_REQUIRE.args(sku));
//            }

            // 处理规格属性
            List<String> specValNameList = new LinkedList<>();

            ProductSku productSku = new ProductSku();
            productSku.setProductId(productId);
            productSku.setProductCode(productCode);
            productSku.setProductSkuCode(productSkuCode);
            productSku.setName(productName);
            productSku.setSku(sku);
            productSku.setErpSku(erpSku);
            productSku.setUpc(upc);
            productSku.setStockManager(stockManagerEnum);
            productSku.setShelfState(ShelfStateEnum.ForcedOffShelf);
            productSku.setVerifyState(ProductVerifyStateEnum.Draft);
            productSku.setSpecComposeName(specComposeName);
            productSku.setSpecValName(specValName);
            productSku.setSort(sort);
            productSku.setStockTotal(0);
            iProductSkuService.insert(productSku);
            Long productSkuId = productSku.getId();

            ProductSkuDetail productSkuDetail = new ProductSkuDetail();
            productSkuDetail.setProductSkuId(productSkuId);
            productSkuDetail.setLength(NumberUtil.toBigDecimal(length));
            productSkuDetail.setWidth(NumberUtil.toBigDecimal(width));
            productSkuDetail.setHeight(NumberUtil.toBigDecimal(height));
            productSkuDetail.setLengthUnit(LengthUnitEnum.valueOf(lengthUnit));
            productSkuDetail.setWeight(NumberUtil.toBigDecimal(weight));
            productSkuDetail.setWeightUnit(WeightUnitEnum.valueOf(weightUnit));

            if (samePacking) {
                packLength = length;
                packWidth = width;
                packHeight = height;
                packLengthUnit = lengthUnit;
                packWeight = weight;
                packWeightUnit = weightUnit;
            }

            productSkuDetail.setPackLength(NumberUtil.toBigDecimal(packLength));
            productSkuDetail.setPackWidth(NumberUtil.toBigDecimal(packWidth));
            productSkuDetail.setPackHeight(NumberUtil.toBigDecimal(packHeight));
            productSkuDetail.setPackLengthUnit(LengthUnitEnum.valueOf(packLengthUnit));
            productSkuDetail.setPackWeight(NumberUtil.toBigDecimal(packWeight));
            productSkuDetail.setPackWeightUnit(WeightUnitEnum.valueOf(packWeightUnit));
            productSkuDetail.setSamePacking(samePacking);
            productSkuDetail.setTransportMethod(transportMethod);
            productSkuDetail.setDescription(description);
            iProductSkuDetailService.insert(productSkuDetail);

            // 处理图片
            List<ProductSkuAttachment> skuAttachmentList = new ArrayList<>();
            for (int i = 0; i < imageList.size(); i++) {
                ProductAttachmentBo image = imageList.get(i);
                ProductSkuAttachment productSkuAttachment = attachmentConvertEntity(image, ProductSkuAttachment.class);
                if (productSkuAttachment == null) {
                    continue;
                }
                productSkuAttachment.setAttachmentSort(i);
                productSkuAttachment.setAttachmentType(AttachmentTypeEnum.Image);
                productSkuAttachment.setProductSkuId(productSkuId);
                skuAttachmentList.add(productSkuAttachment);
            }

            // 处理视频
            for (int i = 0; i < videoList.size(); i++) {
                ProductAttachmentBo video = videoList.get(i);
                ProductSkuAttachment productSkuAttachment = attachmentConvertEntity(video, ProductSkuAttachment.class);
                if (productSkuAttachment == null) {
                    continue;
                }
                productSkuAttachment.setAttachmentSort(i);
                productSkuAttachment.setProductSkuId(productSkuId);
                productSkuAttachment.setAttachmentType(AttachmentTypeEnum.Video);
                skuAttachmentList.add(productSkuAttachment);
            }
            iProductSkuAttachmentService.insertBatch(skuAttachmentList);

            // 处理规格属性
            List<ProductSkuAttribute> skuAttributeList = new ArrayList<>();
            for (int i = 0; i < specComposeList.size(); i++) {
                ProductIntactInfoBo.SpecComposeList specCompose = specComposeList.get(i);
                Long sourceId = specCompose.getSourceId();
                String key = specCompose.getKey();
                String value = specCompose.getValue();

                ProductSkuAttribute skuAttribute = new ProductSkuAttribute();
                skuAttribute.setProductSkuId(productSkuId);
                skuAttribute.setAttributeType(AttributeTypeEnum.OptionalSpec);
                skuAttribute.setAttributeSort(i);
                skuAttribute.setAttributeName(key);
                skuAttribute.setAttributeValue(value);
                skuAttribute.setAttributeSourceId(sourceId);
                skuAttributeList.add(skuAttribute);
            }
            iProductSkuAttributeService.insertBatch(skuAttributeList);

            // 处理库存
            List<ProductSkuStock> skuStockList = new ArrayList<>();
            for (ProductIntactInfoBo.StockConfigList stockConfig : stockConfigList) {
                Integer quantity = stockConfig.getQuantity();
                if (quantity == null) {
                    quantity = 0;
                }
                String warehouseSystemCode = stockConfig.getWarehouseSystemCode();
                String logisticsTemplateNo = stockConfig.getLogisticsTemplateNo();
                ProductSkuStock productSkuStock = new ProductSkuStock();
                String stockCode = productCodeGenerator.codeGenerate(BusinessCodeEnum.StockCode);

                productSkuStock.setStockCode(stockCode);
                productSkuStock.setStockTotal(quantity);
                productSkuStock.setStockReserved(0);
                productSkuStock.setStockAvailable(quantity);
                productSkuStock.setStockState(GlobalStateEnum.Valid);
                productSkuStock.setErpSku(erpSku);
                productSkuStock.setProductCode(productCode);
                productSkuStock.setProductSkuCode(productSkuCode);
                productSkuStock.setWarehouseSystemCode(warehouseSystemCode);
                productSkuStock.setLogisticsTemplateNo(StrUtil.emptyToNull(logisticsTemplateNo));
                productSkuStock.setDropShippingStockAvailable(stockConfig.getDropShippingStockAvailable());

                skuStockList.add(productSkuStock);
            }
            iProductSkuStockService.insertBatch(skuStockList);

            // 非自有仓库需要查询第三方库存
            if (!StockManagerEnum.OwnWarehouse.equals(stockManagerEnum)) {
//                ThirdWarehouseEvent.queryStock(stockManagerEnum, productSkuCode);
            }
            ArrayList<ProductSkuPrice> productSkuPrices = new ArrayList<>();
            List<ProductSitePriceBo> sitePriceList = req_productSku.getSitePriceBos();

            if(CollUtil.isEmpty(sitePriceList)){
                log.info("sitePriceList:{}",sitePriceList);
            }
            // 站点价格
            for (ProductSitePriceBo productSitePriceBo : sitePriceList) {
                ProductSkuPrice productSkuPrice = new ProductSkuPrice();
                BigDecimal siteUnitPrice = productSitePriceBo.getUnitPrice();
                BigDecimal siteFinalDeliveryFee = productSitePriceBo.getFinalDeliveryFee();
                BigDecimal siteOperationFee = productSitePriceBo.getOperationFee();
                productSkuPrice.setOriginalUnitPrice(siteUnitPrice);
                productSkuPrice.setOriginalOperationFee(siteOperationFee);
                productSkuPrice.setOriginalFinalDeliveryFee(siteFinalDeliveryFee);
                productSkuPrice.setOriginalPickUpPrice(NumberUtil.add(siteUnitPrice, siteOperationFee));
                productSkuPrice.setOriginalDropShippingPrice(NumberUtil.add(siteUnitPrice, siteOperationFee, siteFinalDeliveryFee));
                SiteCountryCurrency currency = siteCountryCurrencyMap.get(productSitePriceBo.getSiteId());
                productSkuPrice = iProductSkuPriceRuleService.matchRule(productSkuCode, siteUnitPrice, siteOperationFee, siteFinalDeliveryFee, false);
                productSkuPrice.setProductSkuId(productSkuId);
                productSkuPrice.setProductSkuCode(productSkuCode);
                productSkuPrice.setMsrp(msrp);
                if (ObjectUtil.hasEmpty(siteUnitPrice, siteOperationFee, siteFinalDeliveryFee)) {
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.SKU_INFORMATION_REQUIRE.args(sku));
                }
                if(ObjectUtil.isEmpty(currency)){
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.SITE_INFORMATION_A_SCENE);
                }
                productSkuPrice.setSiteId(productSitePriceBo.getSiteId());
                productSkuPrice.setCountryCode(currency.getCountryCode());
                productSkuPrice.setCurrency(currency.getCurrencyCode());
                productSkuPrice.setCurrencySymbol(currency.getCurrencySymbol());
                productSkuPrice.setProductId(productId);
                productSkuPrices.add(productSkuPrice);
            }

            iProductSkuPriceService.saveBatch(productSkuPrices);
        }

        esProductSupport.productUpload(product);
        return new ProductSaveVo(productCode, false);
    }

    /**
     * 修改商品（完整信息）
     *
     * @param bo
     * @return
     */
    @Override
    @Transactional(rollbackFor = {Exception.class, RStatusCodeException.class})
    public ProductSaveVo updateByIntactInfo(ProductIntactInfoUpdateBo bo) throws RStatusCodeException {
        String productCode = bo.getProductCode();
        if (StrUtil.isBlank(productCode)) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }

        LoginUser loginUser = LoginHelper.getLoginUser(TenantType.Supplier);
        String tenantId = loginUser.getTenantId();
//        String tenantId = "SISY1QG";

        Product product = iProductService.queryByProductCodeNotDelete(productCode);
        if (product != null) {
            Long productId = product.getId();
            SupportedLogisticsEnum beforeSupportedLogistics = product.getSupportedLogistics();
            ProductVerifyStateEnum verifyState = product.getVerifyState();
            Long origin_belongCategoryId = product.getBelongCategoryId();
            // SPU数据变更
            List<ChangeFieldDTO> SPUChangeFields = new ArrayList<>();

            if (ProductVerifyStateEnum.Pending.equals(verifyState)) {
                throw new RStatusCodeException(ZSMallStatusCodeEnum.NEW_PRODUCT_VERIFY_PENDING);
            }
            //处理商品映射状态
            if (!ObjectUtil.equals(product.getSupportedLogistics().name(),bo.getSupportedLogistics())){
                dealSupportedLogisticByProductSkuCode(product,bo.getSupportedLogistics());
            }
            String productName = bo.getProductName();
            Long belongCategoryId = bo.getBelongCategoryId();
            String supportedLogistics = bo.getSupportedLogistics();
            String description = bo.getDescription();
            JSONArray forbiddenChannel = bo.getForbiddenChannel();
            if (CollUtil.isEmpty(forbiddenChannel)) {
                forbiddenChannel = new JSONArray();
            }

            if (belongCategoryId == null || StrUtil.hasBlank(productName, supportedLogistics)) {
                throw new RStatusCodeException(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
            }

            List<ProductIntactInfoUpdateBo.GenericSpecList> genericSpecList = bo.getGenericSpecList();
            List<ProductIntactInfoUpdateBo.OptionalSpecList> optionalSpecList = bo.getOptionalSpecList();
            List<ProductIntactInfoUpdateBo.ProductFeatureList> productFeatureList = bo.getProductFeatureList();
            List<ProductIntactInfoUpdateBo.ProductSkuList> productSkuList = bo.getProductSkuList();

            if (CollUtil.isEmpty(productSkuList)) {
                throw new RStatusCodeException(ZSMallStatusCodeEnum.SKU_INFORMATION_REQUIRE);
            }

            ProductAttachmentBo otherAttachment = bo.getOtherAttachment();

            List<Long> existsAttributeIds = iProductAttributeService.queryIdsByProductId(productId);

            List<ProductAttribute> productAttributeList = new ArrayList<>();
            if (CollUtil.isNotEmpty(genericSpecList)) {
                for (int i = 0; i < genericSpecList.size(); i++) {
                    ProductIntactInfoUpdateBo.GenericSpecList genericSpec = genericSpecList.get(i);
                    Long id = genericSpec.getId();
                    String key = genericSpec.getKey();
                    String value = genericSpec.getValue();
                    Long sourceId = genericSpec.getSourceId();
                    productAttributeList.add(productSupport.setProductAttribute(id, productId, key, value, AttributeTypeEnum.GenericSpec, sourceId, i));
                    // 编辑时依然存在的属性需要从已存在数组中排除，该数组最后需要把仍然在其中的数据逻辑删除
                    existsAttributeIds.remove(id);
                }
            }

            // 处理可选规格
            if (CollUtil.isNotEmpty(optionalSpecList)) {
                for (int i = 0; i < optionalSpecList.size(); i++) {
                    ProductIntactInfoUpdateBo.OptionalSpecList optionalSpec = optionalSpecList.get(i);
                    Long id = optionalSpec.getId();
                    String key = optionalSpec.getKey();
                    Long sourceId = optionalSpec.getSourceId();
                    List<String> values = optionalSpec.getValues();
                    productAttributeList.add(productSupport.setProductAttribute(id, productId, key, values, AttributeTypeEnum.OptionalSpec, sourceId, i));
                    // 编辑时依然存在的属性需要从已存在数组中排除，该数组最后需要把仍然在其中的数据逻辑删除
                    existsAttributeIds.remove(id);
                }
            }

            // 处理商品特色
            if (CollUtil.isNotEmpty(productFeatureList)) {
                for (int i = 0; i < productFeatureList.size(); i++) {
                    ProductIntactInfoUpdateBo.ProductFeatureList feature = productFeatureList.get(i);
                    Long id = feature.getId();
                    String key = feature.getKey();
                    String value = feature.getValue();
                    productAttributeList.add(productSupport.setProductAttribute(id, productId, key, value, AttributeTypeEnum.Feature, null, i));
                    // 编辑时依然存在的属性需要从已存在数组中排除，该数组最后需要把仍然在其中的数据逻辑删除
                    existsAttributeIds.remove(id);
                }
            }

            iProductAttributeService.insertOrUpdateBatch(productAttributeList);
            if (CollUtil.isNotEmpty(existsAttributeIds)) {
                iProductAttributeService.deleteWithValidByIds(existsAttributeIds, false);
            }

            // 处理其他附件
            if (otherAttachment != null && otherAttachment.getOssId() != null) {
                Long originId = otherAttachment.getId();
                String ossId = otherAttachment.getOssId();

                boolean createNew = true;
                if (originId != null) {
                    ProductAttachment productAttachment = iProductAttachmentService.getById(originId);
                    if (productAttachment != null) {
                        createNew = ObjectUtil.notEqual(ossId, productAttachment.getOssId());
                        // 当前提交的附件与存在的附件ossId不一致，需要删除旧的
                        if (createNew) {
                            iProductAttachmentService.removeById(productAttachment);
                        }
                    }
                }

                if (createNew) {
                    ProductAttachment newProductAttachment = attachmentConvertEntity(otherAttachment, ProductAttachment.class);
                    newProductAttachment.setProductId(productId);
                    newProductAttachment.setAttachmentType(AttachmentTypeEnum.File);
                    iProductAttachmentService.insert(newProductAttachment);
                }
            } else {
                iProductAttachmentService.deleteByProductId(productId);
            }

            // 处理分类关系
            if (!origin_belongCategoryId.equals(belongCategoryId)) {
                List<ProductCategory> productCategoryList = iProductCategoryService.queryCategoryChainById(belongCategoryId);
                List<ProductCategoryRelation> oldCategoryRelationList = iProductCategoryRelationService.queryByProductId(productId);
                List<ProductCategoryRelation> newCategoryRelationList = new ArrayList<>();
                if (CollUtil.isNotEmpty(productCategoryList)) {
                    for (ProductCategory productCategory : productCategoryList) {
                        ProductCategoryRelation pcr = new ProductCategoryRelation();
                        pcr.setProductId(productId);
                        pcr.setProductCategoryId(productCategory.getId());
                        newCategoryRelationList.add(pcr);
                    }
                }

                if (CollUtil.isNotEmpty(oldCategoryRelationList)) {
                    iProductCategoryRelationService.deleteByEntityList(oldCategoryRelationList);
                }
                iProductCategoryRelationService.insertBatch(newCategoryRelationList);
            }

            SupportedLogisticsEnum newSupportedLogisticsEnum = SupportedLogisticsEnum.valueOf(supportedLogistics);

            product.setName(productName);
            product.setBelongCategoryId(belongCategoryId);
            product.setSupportedLogistics(newSupportedLogisticsEnum);
            product.setDescription(StrUtil.emptyToNull(description));
            product.setForbiddenChannel(forbiddenChannel);

            SPUChangeFields.add(new ChangeFieldDTO("supportedLogistics", beforeSupportedLogistics.name(),
                product.getSupportedLogistics().name()));

            // 最终需要删除的SKU主键集合
            List<Long> deleteSkuId = iProductSkuService.queryIdsByProductId(productId);
            // 还有效的SKU主键
            List<Long> existsSkuId = productSkuList.stream().filter(item -> ObjectUtil.isNotNull(item.getId())).map(ProductIntactInfoUpdateBo.ProductSkuList::getId).collect(Collectors.toList());
            deleteSkuId.removeAll(existsSkuId);

            // 最终有效的SKU集合
            List<ProductSku> validSkuList = new ArrayList<>();
            // 已存在的SKU价格变更审核DTO
            List<ProductSkuReviewDTO> productSkuReviewList = new ArrayList<>();
            // 新的SKU价格变更审核DTO
            List<ProductSkuReviewDTO> newProductSkuReviewList = new ArrayList<>();
            List<ProductSkuPrice> newProductSkuPriceList = new ArrayList<>();


            // validSkuId不为空时，则说明有的SKU已被前端删除，此时要删除数据库中的数据
            if (CollUtil.isNotEmpty(deleteSkuId)) {
                iProductSkuService.deleteWithValidByIds(deleteSkuId, false);
                // iProductSkuPriceService.deleteByProductSkuIdList(deleteSkuId);
                // iProductSkuDetailService.deleteByProductSkuIdList(deleteSkuId);
                iProductSkuStockService.deleteByProductSkuIdList(deleteSkuId);
                iProductSkuPriceRuleRelationService.deleteByProductSkuIdList(deleteSkuId);
                iProductSkuPriceRuleRelationService.deleteByProductSkuIdList(deleteSkuId);
            }

            // 处理SKU数据
            for (int sort = 0; sort < productSkuList.size(); sort++) {
                ProductIntactInfoUpdateBo.ProductSkuList req_productSku = productSkuList.get(sort);
//                List<Long> removePriceIds = req_productSku.getRemovePriceIds();
                // 加校验-如果移除的价格Id数量等于产品的站点价格数量就报错

                List<ProductSitePriceBo> sitePriceList = req_productSku.getSitePriceBos();
                Long productSkuId = req_productSku.getId();
                String sku = req_productSku.getSku();
                String erpSku = req_productSku.getErpSku();
                String upc = req_productSku.getUpc();
                List<ProductAttachmentBo> imageList = req_productSku.getImageList();
                List<ProductIntactInfoUpdateBo.SpecComposeList> specComposeList = req_productSku.getSpecComposeList();
                List<ProductIntactInfoUpdateBo.StockConfigList> stockConfigList = req_productSku.getStockConfigList();

                boolean existSku = iProductSkuService.existSku(sku, tenantId, productSkuId);
                LambdaQueryWrapper<ProductSkuPrice> skuLambdaQueryWrapper = new LambdaQueryWrapper<>();
                skuLambdaQueryWrapper.eq(ProductSkuPrice::getProductSkuId, productSkuId);
                skuLambdaQueryWrapper.eq(ProductSkuPrice::getDelFlag,0);
                List<ProductSkuPrice> roductSkuPrices = iProductSkuPriceService.list(skuLambdaQueryWrapper);
                if(CollUtil.isEmpty(roductSkuPrices)){
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.PRODUCT_SKU_NOT_EXIST);
                }
                // productSkus 转化成 key是siteId,value是对应元素的map
                Map<Long, ProductSkuPrice> productSkuPriceMap = roductSkuPrices.stream().collect(Collectors.toMap(ProductSkuPrice::getSiteId, Function.identity()));
                if (existSku) {
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.PRODUCT_SKU_CODE_REPEAT.args(sku));
                }

                if (StrUtil.isNotBlank(upc)) {
                    boolean existUpc = iProductSkuService.existUpc(upc, productSkuId);
                    if (existUpc) {
                        throw new RStatusCodeException(ZSMallStatusCodeEnum.PRODUCT_UPC_CODE_REPEAT.args(upc));
                    }
                } else {
                    upc = null;
                }

                if (CollUtil.isEmpty(imageList)) {
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.SKU_REQUIRES_ONE_IMAGE);
                }
                if (CollUtil.isEmpty(stockConfigList)) {
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.WAREHOUSE_NOT_SELECTED);
                }
                if (CollUtil.isEmpty(specComposeList)) {
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
                }



                String stockManager = req_productSku.getStockManager();
                if(StringUtils.isEmpty(stockManager)){
                    stockManager = StockManagerEnum.BizArk.name();
                }
                StockManagerEnum stockManagerEnum = StockManagerEnum.valueOf(stockManager);

                //BigDecimal finalDeliveryFee = BigDecimal.ZERO;
                String specValName = req_productSku.getSpecValName();
                String specComposeName = req_productSku.getSpecComposeName();


                BigDecimal msrp = req_productSku.getMsrp();
                String length = req_productSku.getLength();
                String width = req_productSku.getWidth();
                String height = req_productSku.getHeight();
                String lengthUnit = req_productSku.getLengthUnit();
                String weight = req_productSku.getWeight();
                String weightUnit = req_productSku.getWeightUnit();
                String packLength = req_productSku.getPackLength();
                String packWidth = req_productSku.getPackWidth();
                String packHeight = req_productSku.getPackHeight();
                String packLengthUnit = req_productSku.getPackLengthUnit();
                String packWeight = req_productSku.getPackWeight();
                String packWeightUnit = req_productSku.getPackWeightUnit();
                boolean samePacking = req_productSku.isSamePacking();
                String transportMethod = req_productSku.getTransportMethod();
                List<ProductAttachmentBo> videoList = req_productSku.getVideoList();

                // 处理规格属性
                List<String> specValNameList = new LinkedList<>();

                ProductSku productSku;
                if (productSkuId != null) {
                    productSku = iProductSkuService.queryByIdAndProductId(productSkuId, productId);
                } else {
                    String productSkuCode = productCodeGenerator.codeGenerate(BusinessCodeEnum.ProductSkuCode);
                    productSku = new ProductSku();
                    productSku.setShelfState(ShelfStateEnum.ForcedOffShelf);
                    productSku.setVerifyState(ProductVerifyStateEnum.Draft);
                    productSku.setProductCode(productCode);
                    productSku.setProductSkuCode(productSkuCode);
                }

                String productSkuCode = productSku.getProductSkuCode();
                String oldSpecValName = productSku.getSpecValName();

                if (StrUtil.isNotBlank(stockManager)) {
                    List<Long> deleteSkuStockIds = iProductSkuStockService.queryIdsByProductSkuCode(productSkuCode);
                    // 处理库存
                    List<ProductSkuStock> skuStockList = new ArrayList<>();
                    for (var stockConfig : stockConfigList) {
                        Integer quantity = stockConfig.getQuantity();
                        if (quantity == null) {
                            quantity = 0;
                        }

                        String warehouseSystemCode = stockConfig.getWarehouseSystemCode();
                        String logisticsTemplateNo = stockConfig.getLogisticsTemplateNo();
                        ProductSkuStock productSkuStock = iProductSkuStockService.queryByProductSkuCode(productSkuCode, warehouseSystemCode);
                        if (productSkuStock == null) {
                            productSkuStock = new ProductSkuStock();
                            productSkuStock.setStockState(GlobalStateEnum.Valid);
                        } else {
                            deleteSkuStockIds.remove(productSkuStock.getId());
                        }

                        if (StockManagerEnum.OwnWarehouse.equals(stockManagerEnum)) {
                            productSkuStock.setStockTotal(quantity);
                            productSkuStock.setStockReserved(0);
                            productSkuStock.setStockAvailable(quantity);
                        } else {  // 第三方仓储系统的库存，先全部置为零，后续调用接口同步
//                            productSkuStock.setStockTotal(0);
//                            productSkuStock.setStockReserved(0);
//                            productSkuStock.setStockAvailable(0);
                            productSkuStock.setStockTotal(quantity);
                            productSkuStock.setStockReserved(0);
                            productSkuStock.setStockAvailable(quantity);
                        }

                        String stockCode = productCodeGenerator.codeGenerate(BusinessCodeEnum.StockCode);
                        productSkuStock.setStockCode(stockCode);
                        productSkuStock.setErpSku(erpSku);
                        productSkuStock.setProductCode(productCode);
                        productSkuStock.setProductSkuCode(productSkuCode);
                        productSkuStock.setWarehouseSystemCode(warehouseSystemCode);
                        productSkuStock.setLogisticsTemplateNo(StrUtil.emptyToNull(logisticsTemplateNo));
                        productSkuStock.setDropShippingStockAvailable(stockConfig.getDropShippingStockAvailable());

                        skuStockList.add(productSkuStock);
                    }
                    iProductSkuStockService.saveOrUpdateBatch(skuStockList);
                    if (CollUtil.isNotEmpty(deleteSkuStockIds)) {
                        iProductSkuStockService.deleteWithValidByIds(deleteSkuStockIds, false);
                    }
                } else {
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.WAREHOUSE_NOT_SELECTED);
                }

                productSku.setProductId(productId);
                productSku.setName(productName);
                productSku.setSku(sku);
                productSku.setErpSku(erpSku);
                productSku.setUpc(StrUtil.emptyToNull(upc));
                productSku.setStockManager(stockManagerEnum);
                productSku.setSpecValName(specValName);
                productSku.setSpecComposeName(specComposeName);
                productSku.setSort(sort);
                iProductSkuService.saveOrUpdate(productSku);
                productSkuId = productSku.getId();

                // 处理图片
                List<Long> skuAttachmentDeleteIds = iProductSkuAttachmentService.queryIdsByProductSkuId(productSkuId);
                List<ProductSkuAttachment> skuAttachmentList = new ArrayList<>();
                for (int i = 0; i < imageList.size(); i++) {
                    ProductAttachmentBo image = imageList.get(i);
                    ProductSkuAttachment productSkuAttachment = attachmentConvertEntity(image, ProductSkuAttachment.class);
                    if (productSkuAttachment == null) {
                        continue;
                    }
                    productSkuAttachment.setAttachmentSort(i);
                    productSkuAttachment.setProductSkuId(productSkuId);
                    productSkuAttachment.setAttachmentType(AttachmentTypeEnum.Image);
                    skuAttachmentList.add(productSkuAttachment);
                    skuAttachmentDeleteIds.remove(productSkuAttachment.getId());
                }

                // 处理视频
                for (int i = 0; i < videoList.size(); i++) {
                    ProductAttachmentBo video = videoList.get(i);
                    ProductSkuAttachment productSkuAttachment = attachmentConvertEntity(video, ProductSkuAttachment.class);
                    if (productSkuAttachment == null) {
                        continue;
                    }
                    productSkuAttachment.setAttachmentSort(i);
                    productSkuAttachment.setProductSkuId(productSkuId);
                    productSkuAttachment.setAttachmentType(AttachmentTypeEnum.Video);
                    skuAttachmentList.add(productSkuAttachment);
                    skuAttachmentDeleteIds.remove(productSkuAttachment.getId());
                }
                iProductSkuAttachmentService.saveOrUpdateBatch(skuAttachmentList);
                if (CollUtil.isNotEmpty(skuAttachmentDeleteIds)) {
                    iProductSkuAttachmentService.removeBatchByIds(skuAttachmentDeleteIds);
                }

                // 处理规格属性，编辑时，如果之前的规格组合和现在的不一样，才需要新增记录
                if (oldSpecValName == null || !StrUtil.equals(oldSpecValName, specValName)) {
                    List<ProductSkuAttribute> skuAttributeList = new ArrayList<>();
                    for (int i = 0; i < specComposeList.size(); i++) {
                        ProductIntactInfoUpdateBo.SpecComposeList specCompose = specComposeList.get(i);
                        Long sourceId = specCompose.getSourceId();
                        String key = specCompose.getKey();
                        String value = specCompose.getValue();

                        ProductSkuAttribute skuAttribute = new ProductSkuAttribute();
                        skuAttribute.setProductSkuId(productSkuId);
                        skuAttribute.setAttributeType(AttributeTypeEnum.OptionalSpec);
                        skuAttribute.setAttributeSort(i);
                        skuAttribute.setAttributeName(key);
                        skuAttribute.setAttributeValue(value);
                        skuAttribute.setAttributeSourceId(sourceId);
                        skuAttributeList.add(skuAttribute);
                    }

                    List<ProductSkuAttribute> oldSkuAttributeList = iProductSkuAttributeService.queryByProductSkuId(productSkuId);
                    if (CollUtil.isNotEmpty(oldSkuAttributeList)) {
                        iProductSkuAttributeService.deleteByEntityList(oldSkuAttributeList);
                    }

                    iProductSkuAttributeService.insertBatch(skuAttributeList);
                }

                ProductSkuDetail productSkuDetail = iProductSkuDetailService.queryByProductSkuId(productSkuId);
                if (productSkuDetail == null) {
                    productSkuDetail = new ProductSkuDetail();
                    productSkuDetail.setProductSkuId(productSkuId);
                }

                productSkuDetail.setLength(NumberUtil.toBigDecimal(length));
                productSkuDetail.setWidth(NumberUtil.toBigDecimal(width));
                productSkuDetail.setHeight(NumberUtil.toBigDecimal(height));
                productSkuDetail.setLengthUnit(LengthUnitEnum.valueOf(lengthUnit));
                productSkuDetail.setWeight(NumberUtil.toBigDecimal(weight));
                productSkuDetail.setWeightUnit(WeightUnitEnum.valueOf(weightUnit));

                if (samePacking) {
                    packLength = length;
                    packWidth = width;
                    packHeight = height;
                    packLengthUnit = lengthUnit;
                    packWeight = weight;
                    packWeightUnit = weightUnit;
                }

                productSkuDetail.setPackLength(NumberUtil.toBigDecimal(packLength));
                productSkuDetail.setPackWidth(NumberUtil.toBigDecimal(packWidth));
                productSkuDetail.setPackHeight(NumberUtil.toBigDecimal(packHeight));
                productSkuDetail.setPackLengthUnit(LengthUnitEnum.valueOf(packLengthUnit));
                productSkuDetail.setPackWeight(NumberUtil.toBigDecimal(packWeight));
                productSkuDetail.setPackWeightUnit(WeightUnitEnum.valueOf(packWeightUnit));
                productSkuDetail.setSamePacking(samePacking);
                productSkuDetail.setTransportMethod(transportMethod);
                productSkuDetail.setDescription(description);
                iProductSkuDetailService.insertOrUpdate(productSkuDetail);
                // 修改和删除和新增价格
                ShelfStateEnum skuShelfState = null;

                if(CollUtil.isNotEmpty(sitePriceList)){
                    Boolean isEditor = false;
                    for (ProductSitePriceBo productSitePriceBo : sitePriceList) {

                        Long siteId = productSitePriceBo.getSiteId();
                        if(ObjectUtil.isEmpty(productSitePriceBo.getId())){
                            ProductSkuPrice productSkuPrice = iProductSkuPriceService.queryByProductSkuCodeAndSite(productSkuCode, siteId);;
                            if(ObjectUtil.isNotEmpty(productSkuPrice)){
                                productSitePriceBo.setId(productSkuPrice.getId());

                            }
                        }
//                        if(CollUtil.isNotEmpty(removePriceIds)){
//                            removePriceIds.remove(productSitePriceBo.getId());
//                        }
                        BigDecimal unitPrice = productSitePriceBo.getUnitPrice();
                        BigDecimal operationFee = productSitePriceBo.getOperationFee();
                        //取消商品尾程派送费逻辑，改为0
                        BigDecimal finalDeliveryFee = productSitePriceBo.getFinalDeliveryFee();
                        if (SupportedLogisticsEnum.PickUpOnly.equals(newSupportedLogisticsEnum) && finalDeliveryFee == null) {
                            finalDeliveryFee = BigDecimal.ZERO;
                        }

                        if (ObjectUtil.hasEmpty(unitPrice, operationFee, finalDeliveryFee)) {
                            throw new RStatusCodeException(ZSMallStatusCodeEnum.SKU_INFORMATION_REQUIRE.args(sku));
                        }

                        BigDecimal pickUpPrice = NumberUtil.add(unitPrice, operationFee);
                        BigDecimal dropShippingPrice = NumberUtil.add(pickUpPrice, finalDeliveryFee);

                        boolean isOnShelf = req_productSku.getIsOnShelf();
                        skuShelfState = ShelfStateEnum.OffShelf;
                        if (isOnShelf) {
                            skuShelfState = ShelfStateEnum.OnShelf;
                        }
                        ProductSkuPrice isSiteExist = iProductSkuPriceService.queryByProductSkuCodeAndSite(productSkuCode, siteId);
                        // todo 修改新增无法通过id判断,必须通过countryCode来做校验,如果code已存在就是修改,不存在就是新增
                        if(ObjectUtil.isEmpty(isSiteExist)){
                            // 走新增价格审批流程
                            // 判断站点是否已经存在,如果存在相同站点,直接抛出异常
                            if(productSkuPriceMap.containsKey(siteId)){
                                throw new RStatusCodeException("站点已存在");
                            }
                            BigDecimal originalUnitPrice = productSitePriceBo.getUnitPrice();
                            BigDecimal originalOperationFee = productSitePriceBo.getOperationFee();
                            BigDecimal originalFinalDeliveryFee = productSitePriceBo.getFinalDeliveryFee();
                            if (ObjectUtil.isEmpty(originalUnitPrice)) {
                                originalUnitPrice = BigDecimal.ZERO;
                            }
                            if (ObjectUtil.isEmpty(originalOperationFee)) {
                                originalOperationFee = BigDecimal.ZERO;
                            }
                            if (ObjectUtil.isEmpty(originalFinalDeliveryFee)) {
                                originalFinalDeliveryFee = BigDecimal.ZERO;
                            }
                            BigDecimal beforeMsrp = BigDecimal.ZERO;
                            BigDecimal beforeDropShippingPrice = originalUnitPrice.add(originalOperationFee).add(originalFinalDeliveryFee);
                            BigDecimal beforePickUpPrice = originalUnitPrice.add(originalOperationFee);
                            //  记录变更字段及值
                            ProductSkuReviewDTO productSkuReviewDTO = new ProductSkuReviewDTO();
                            productSkuReviewDTO.setSiteId(siteId);
                            productSkuReviewDTO.setProductSkuCode(productSkuCode);
                            // add 操作没有id,但是add流程也要把价格加进去,现在有的逻辑可能会直接报错
                            productSkuReviewDTO.setReviewType(PriceOperateLog.Add.name());
                            productSkuReviewDTO.addField("msrp", NumberUtil.toStr(beforeMsrp), NumberUtil.toStr(beforeMsrp, "0"));
                            productSkuReviewDTO.addField("dropShippingPrice", NumberUtil.toStr(beforeDropShippingPrice),
                                NumberUtil.toStr(beforeDropShippingPrice, "0"));
                            productSkuReviewDTO.addField("pickUpPrice", NumberUtil.toStr(beforePickUpPrice),
                                NumberUtil.toStr(beforePickUpPrice, "0"));
                            productSkuReviewDTO.addField("unitPrice", NumberUtil.toStr(originalUnitPrice),
                                NumberUtil.toStr(originalUnitPrice, "0"));
                            productSkuReviewDTO.addField("operationFee", NumberUtil.toStr(originalOperationFee),
                                NumberUtil.toStr(originalOperationFee, "0"));
                            productSkuReviewDTO.addField("finalDeliveryFee", NumberUtil.toStr(originalFinalDeliveryFee),
                                NumberUtil.toStr(originalFinalDeliveryFee, "0"));
                            productSku.setVerifyState(ProductVerifyStateEnum.Pending);

                            productSkuReviewList.add(productSkuReviewDTO);
                            ProductSkuPrice productSkuPrice = new ProductSkuPrice();
                            productSkuPrice.setProductSkuId(productSkuId);
                            if(ObjectUtil.isNotEmpty(productId)){
                                productSkuPrice.setProductId(productId);
                            }
                            productSkuPrice.setSiteId(siteId);
                            productSkuPrice.setCountryCode(productSitePriceBo.getCountryCode());
                            productSkuPrice.setCurrencySymbol(productSitePriceBo.getCurrencySymbol());
                            productSkuPrice.setCurrency(productSitePriceBo.getCurrencyCode());

                            productSkuPrice.setProductSkuCode(productSkuCode);
                            productSkuPrice.setMsrp(BigDecimal.ZERO);
                            productSkuPrice.setOriginalUnitPrice(originalUnitPrice);
                            productSkuPrice.setOriginalOperationFee(originalOperationFee);
                            productSkuPrice.setOriginalFinalDeliveryFee(originalFinalDeliveryFee);
                            productSkuPrice.setOriginalPickUpPrice(beforePickUpPrice);
                            productSkuPrice.setOriginalDropShippingPrice(beforeDropShippingPrice);
                            productSkuPrice.setPlatformUnitPrice(originalUnitPrice);
                            productSkuPrice.setPlatformOperationFee(originalOperationFee);
                            productSkuPrice.setPlatformFinalDeliveryFee(originalFinalDeliveryFee);
                            productSkuPrice.setPlatformPickUpPrice(beforePickUpPrice);
                            productSkuPrice.setPlatformDropShippingPrice(beforeDropShippingPrice);
                            newProductSkuPriceList.add(productSkuPrice);
                            isEditor=true;
                        }else {
                            // 走修改
                            ProductSkuPrice productSkuPrice = iProductSkuPriceService.queryByProductSkuCodeAndSite(productSkuCode, siteId);
//                            // todo 增加校验,不允许在已有站点上切换站点
//                            Long priceSiteId = isSiteExist.getSiteId();
//                            if(!priceSiteId.equals(siteId)){
//                                throw new RStatusCodeException("产品:"+productSkuCode+","+productSitePriceBo.getCountryCode()+"站点已存在,请在已有站点上修改价格!");
//                            }

//                ProductSkuPrice matchRule = iProductSkuPriceRuleService.matchRule(productSkuCode, unitPrice, operationFee, finalDeliveryFee, false);
//                Long newPriceRuleId = matchRule.getProductSkuPriceRuleId();
                            // 当前版本没有输入的入口,默认给0
                            BigDecimal beforeMsrp = BigDecimal.ZERO;
                            BigDecimal beforeUnitPrice = productSkuPrice.getOriginalUnitPrice();
                            BigDecimal beforeOperationFee = productSkuPrice.getOriginalOperationFee();
                            BigDecimal beforeFinalDeliveryFee = productSkuPrice.getOriginalFinalDeliveryFee();
                            BigDecimal beforePickUpPrice = productSkuPrice.getOriginalPickUpPrice();
                            BigDecimal beforeDropShippingPrice = productSkuPrice.getOriginalDropShippingPrice();
                            // 还原丢失代码

                            BigDecimal unitPriceMd = productSitePriceBo.getUnitPrice();
                            BigDecimal operationFeeMd = productSitePriceBo.getOperationFee();
                            BigDecimal finalDeliveryFeeMd = productSitePriceBo.getFinalDeliveryFee();
                            BigDecimal pickUpPriceMd = unitPriceMd.add(operationFeeMd);
                            BigDecimal dropShippingPriceMd = pickUpPriceMd.add(finalDeliveryFeeMd);
                            // 如果价格没有发生变化则跳过
                            if (beforeUnitPrice.compareTo(unitPriceMd) == 0 &&
                                beforeOperationFee.compareTo(operationFeeMd) == 0 &&
                                beforeFinalDeliveryFee.compareTo(finalDeliveryFeeMd) == 0 &&
                                beforePickUpPrice.compareTo(pickUpPriceMd) == 0 &&
                                beforeDropShippingPrice.compareTo(dropShippingPriceMd) == 0) {
                                // If prices have not changed, skip this iteration
                                continue;
                            }
                            // 商品SPU未过审或者被拒绝时，价格变更直接生效 ----此处为编辑的逻辑
                            if (ProductVerifyStateEnum.Draft.equals(verifyState) || ProductVerifyStateEnum.Rejected.equals(verifyState)) {
                                // 站点信息
                                productSkuPrice.setMsrp(msrp);

                                productSkuPrice.setCurrency(productSitePriceBo.getCurrencyCode());
                                productSkuPrice.setCurrencySymbol(productSitePriceBo.getCurrencySymbol());
                                productSkuPrice.setCountryCode(productSitePriceBo.getCountryCode());
                                productSkuPrice.setSiteId(siteId);

                                productSkuPrice.setOriginalUnitPrice(unitPrice);
                                productSkuPrice.setOriginalOperationFee(operationFee);
                                productSkuPrice.setOriginalFinalDeliveryFee(finalDeliveryFee);
                                productSkuPrice.setOriginalPickUpPrice(pickUpPrice);
                                productSkuPrice.setOriginalDropShippingPrice(dropShippingPrice);
                                productSkuPrice.setPlatformUnitPrice(unitPrice);
                                productSkuPrice.setPlatformOperationFee(operationFee);
                                productSkuPrice.setPlatformFinalDeliveryFee(finalDeliveryFee);
                                productSkuPrice.setPlatformPickUpPrice(pickUpPrice);
                                productSkuPrice.setPlatformDropShippingPrice(dropShippingPrice);

                                skuShelfState = ShelfStateEnum.ForcedOffShelf;
                                productSku.setVerifyState(ProductVerifyStateEnum.Draft);
                                isEditor=true;
                            } else if (ProductVerifyStateEnum.Accepted.equals(verifyState)) {  // 商品SPU已过审
                                ProductVerifyStateEnum skuVerifyState = productSku.getVerifyState();
                                // 但Sku未过审或被拒绝时
                                if (ProductVerifyStateEnum.Draft.equals(skuVerifyState) || ProductVerifyStateEnum.Rejected.equals(skuVerifyState)) {
                                    productSkuPrice.setMsrp(msrp);
                                    productSkuPrice.setOriginalUnitPrice(unitPrice);
                                    productSkuPrice.setCurrency(productSitePriceBo.getCurrencyCode());
                                    productSkuPrice.setCurrencySymbol(productSitePriceBo.getCurrencySymbol());
                                    productSkuPrice.setCountryCode(productSitePriceBo.getCountryCode());
                                    productSkuPrice.setSiteId(siteId);
                                    productSkuPrice.setOriginalOperationFee(operationFee);
                                    productSkuPrice.setOriginalFinalDeliveryFee(finalDeliveryFee);
                                    productSkuPrice.setOriginalPickUpPrice(pickUpPrice);
                                    productSkuPrice.setOriginalDropShippingPrice(dropShippingPrice);
                                    productSkuPrice.setPlatformUnitPrice(unitPriceMd);
                                    productSkuPrice.setPlatformOperationFee(operationFeeMd);
                                    productSkuPrice.setPlatformFinalDeliveryFee(finalDeliveryFeeMd);
                                    productSkuPrice.setPlatformPickUpPrice(pickUpPriceMd);
                                    productSkuPrice.setPlatformDropShippingPrice(dropShippingPriceMd);

                                    //如果价格信息已存在，则原价存入价格日志表，新价格存入价格表
                                    if (ObjectUtil.isNotNull(productSkuPrice.getId())) {
                                        //记录价格日志
                                        iProductSkuPriceLogService.recordPriceChanges(productSkuPrice, PriceOperateLog.Update.name());
                                    }

                                    // 记录变更字段及值
                                    ProductSkuReviewDTO productSkuReviewDTO = new ProductSkuReviewDTO();
                                    productSkuReviewDTO.setProductSkuCode(productSkuCode);
                                    productSkuReviewDTO.setProductSkuPriceId(productSitePriceBo.getId());
                                    productSkuReviewDTO.setReviewType(PriceOperateLog.Update.name());

                                    productSkuReviewDTO.addField("msrp", NumberUtil.toStr(beforeMsrp), NumberUtil.toStr(msrp, "0"));
                                    productSkuReviewDTO.addField("pickUpPrice", NumberUtil.toStr(beforePickUpPrice), NumberUtil.toStr(pickUpPrice, "0"));
                                    productSkuReviewDTO.addField("dropShippingPrice", NumberUtil.toStr(beforeDropShippingPrice), NumberUtil.toStr(dropShippingPrice, "0"));
                                    productSkuReviewDTO.addField("unitPrice", NumberUtil.toStr(beforeUnitPrice), NumberUtil.toStr(unitPrice, "0"));
                                    productSkuReviewDTO.addField("operationFee", NumberUtil.toStr(beforeOperationFee), NumberUtil.toStr(operationFee, "0"));
                                    productSkuReviewDTO.addField("finalDeliveryFee", NumberUtil.toStr(beforeFinalDeliveryFee), NumberUtil.toStr(finalDeliveryFee, "0"));
                                    newProductSkuReviewList.add(productSkuReviewDTO);
                                    productSku.setVerifyState(ProductVerifyStateEnum.Pending);
                                    skuShelfState = ShelfStateEnum.ForcedOffShelf;
                                    isEditor=true;
                                } else if (ProductVerifyStateEnum.Pending.equals(skuVerifyState)) {  // 正在审核中时，不接受新修改的价格
                                    skuShelfState = ShelfStateEnum.ForcedOffShelf;
                                } else if (ProductVerifyStateEnum.Accepted.equals(skuVerifyState)) {  // 已通过审核时，需要提交价格变更审核
                                    if (!NumberUtil.equals(beforeMsrp, msrp)
                                        || !NumberUtil.equals(beforeDropShippingPrice, dropShippingPrice)
                                        || !NumberUtil.equals(beforePickUpPrice, pickUpPrice)) {
                                        // 记录变更字段及值
                                        ProductSkuReviewDTO productSkuReviewDTO = new ProductSkuReviewDTO();
                                        productSkuReviewDTO.setProductSkuPriceId(productSitePriceBo.getId());
                                        productSkuReviewDTO.setSiteId(siteId);
                                        productSkuReviewDTO.setReviewType(PriceOperateLog.Update.name());
                                        productSkuReviewDTO.setProductSkuCode(productSkuCode);
                                        productSkuReviewDTO.addField("msrp", NumberUtil.toStr(beforeMsrp), NumberUtil.toStr(msrp, "0"));
                                        productSkuReviewDTO.addField("dropShippingPrice", NumberUtil.toStr(beforeDropShippingPrice), NumberUtil.toStr(dropShippingPrice, "0"));
                                        productSkuReviewDTO.addField("pickUpPrice", NumberUtil.toStr(beforePickUpPrice), NumberUtil.toStr(pickUpPrice, "0"));
                                        productSkuReviewDTO.addField("unitPrice", NumberUtil.toStr(beforeUnitPrice), NumberUtil.toStr(unitPrice, "0"));
                                        productSkuReviewDTO.addField("operationFee", NumberUtil.toStr(beforeOperationFee), NumberUtil.toStr(operationFee, "0"));
                                        productSkuReviewDTO.addField("finalDeliveryFee", NumberUtil.toStr(beforeFinalDeliveryFee), NumberUtil.toStr(finalDeliveryFee, "0"));
                                        productSkuReviewDTO.addField("ruleId", StrUtil.toString(1), StrUtil.toString(1));
                                        productSkuReviewList.add(productSkuReviewDTO);
                                        isEditor=true;
                                    }

                                }
                            }

                            newProductSkuPriceList.add(productSkuPrice);
                        }

                    }
                    // 前端不给了,这里要自己做对比, 查出所有的ids, 1.先查已经有的数据
                    // todo removeIds怎么获取, 1.前端给的站点集合和查出来的站点集合进行对比 ,少了的站点就是要删除的站点
                    List<ProductSitePriceBo> productSitePriceBos = req_productSku.getSitePriceBos();
                    //productSitePriceBos 转换成站点集合
                    List<String> newCountryCodes = productSitePriceBos.stream().map(ProductSitePriceBo::getCountryCode)
                                                              .collect(Collectors.toList());
                    List<Long> newIds = productSitePriceBos.stream().map(ProductSitePriceBo::getId)
                                                            .collect(Collectors.toList());
                    List<ProductSkuPrice> list = iProductSkuPriceService.queryListByProductSkuCode(productSkuCode);
                    List<String> oldCountryCodes = list.stream().map(ProductSkuPrice::getCountryCode)
                                               .collect(Collectors.toList());
                    Set<String> newCountryCodeSet = new HashSet<>(newCountryCodes);

                    List<String> elementsNotInNewCountryCodes = oldCountryCodes.stream()
                                                                               .filter(code -> !newCountryCodeSet.contains(code))
                                                                               .collect(Collectors.toList());
                    List<Long> removePriceIds = list.stream()
                                                 .filter(item -> elementsNotInNewCountryCodes.contains(item.getCountryCode()))
                                                 .map(ProductSkuPrice::getId)
                                                 .collect(Collectors.toList());
                    // 查到oldCountryCodes内有newCountryCodes没有的元素,就是要删除的元素,并找到list中对应的id

                    if(CollUtil.isNotEmpty(removePriceIds)){
//                        List<ProductSkuPrice> list = iProductSkuPriceService.queryListByProductSkuCode(productSkuCode);
                        // todo 移除的数量<list+新增的数量,否则报错
                        if (CollUtil.isNotEmpty(removePriceIds) && CollUtil.isNotEmpty(list) && removePriceIds.size() >= newCountryCodes.size()+ list.size() ) {
                            throw new RStatusCodeException("至少保留一条价格信息");
                        }
                        for (Long removePriceId : removePriceIds) {

                            ProductSkuPrice productSkuPrice = iProductSkuPriceService.getById(removePriceId);
                            BigDecimal beforeMsrp = productSkuPrice.getMsrp();
                            BigDecimal beforeUnitPrice = productSkuPrice.getOriginalUnitPrice();
                            BigDecimal beforeOperationFee = productSkuPrice.getOriginalOperationFee();
                            BigDecimal beforeFinalDeliveryFee = productSkuPrice.getOriginalFinalDeliveryFee();
                            BigDecimal beforePickUpPrice = productSkuPrice.getOriginalPickUpPrice();
                            BigDecimal beforeDropShippingPrice = productSkuPrice.getOriginalDropShippingPrice();
                            if (ProductVerifyStateEnum.Draft.equals(verifyState) || ProductVerifyStateEnum.Rejected.equals(verifyState)) {
                                skuShelfState = ShelfStateEnum.ForcedOffShelf;
                                productSku.setVerifyState(ProductVerifyStateEnum.Draft);
                            } else if (ProductVerifyStateEnum.Accepted.equals(verifyState)) {  // 商品SPU已过审
                                ProductVerifyStateEnum skuVerifyState = productSku.getVerifyState();
                                // 但Sku未过审或被拒绝时 -有同时新增和修改和删除的逻辑,所以这里不能拿
                                if (ProductVerifyStateEnum.Draft.equals(skuVerifyState) || ProductVerifyStateEnum.Rejected.equals(skuVerifyState)) {


                                    if (ObjectUtil.isNotNull(productSkuPrice.getId())) {
                                        //记录价格日志
                                        iProductSkuPriceLogService.recordPriceChanges(productSkuPrice, PriceOperateLog.Delete.name());
                                    }

                                    // 记录变更字段及值
                                    ProductSkuReviewDTO productSkuReviewDTO = new ProductSkuReviewDTO();
                                    productSkuReviewDTO.setSiteId(productSkuPrice.getSiteId());
                                    productSkuReviewDTO.setProductSkuPriceId(productSkuPrice.getId());
                                    productSkuReviewDTO.setProductSkuCode(productSkuCode);
                                    productSkuReviewDTO.setReviewType(PriceOperateLog.Delete.name());
                                    productSkuReviewDTO.addField("msrp", NumberUtil.toStr(beforeMsrp), NumberUtil.toStr(msrp, "0"));
                                    productSkuReviewDTO.addField("pickUpPrice", NumberUtil.toStr(beforePickUpPrice), NumberUtil.toStr(beforePickUpPrice, "0"));
                                    productSkuReviewDTO.addField("dropShippingPrice", NumberUtil.toStr(beforeDropShippingPrice), NumberUtil.toStr(beforeDropShippingPrice, "0"));
                                    productSkuReviewDTO.addField("unitPrice", NumberUtil.toStr(beforeUnitPrice), NumberUtil.toStr(beforeUnitPrice, "0"));
                                    productSkuReviewDTO.addField("operationFee", NumberUtil.toStr(beforeOperationFee), NumberUtil.toStr(beforeOperationFee, "0"));
                                    productSkuReviewDTO.addField("finalDeliveryFee", NumberUtil.toStr(beforeFinalDeliveryFee), NumberUtil.toStr(beforeFinalDeliveryFee, "0"));
                                    newProductSkuReviewList.add(productSkuReviewDTO);
                                    productSku.setVerifyState(ProductVerifyStateEnum.Pending);
                                    skuShelfState = ShelfStateEnum.ForcedOffShelf;
                                } else if (ProductVerifyStateEnum.Pending.equals(skuVerifyState)&&!isEditor) {  // 正在审核中时，不接受新修改的价格 -有同时新增和修改和删除的逻辑,所以这里不能拿Pending做限制了
                                    skuShelfState = ShelfStateEnum.ForcedOffShelf;
                                } else if (ProductVerifyStateEnum.Accepted.equals(skuVerifyState)||isEditor) {  // 已通过审核时，需要提交价格变更审核
                                    if (ObjectUtil.isNotNull(productSkuPrice.getId())) {
                                        //记录价格日志
                                        iProductSkuPriceLogService.recordPriceChanges(productSkuPrice, PriceOperateLog.Delete.name());
                                    }
                                    ProductSkuReviewDTO productSkuReviewDTO = new ProductSkuReviewDTO();
                                    productSkuReviewDTO.setSiteId(productSkuPrice.getSiteId());
                                    productSkuReviewDTO.setReviewType(PriceOperateLog.Delete.name());
                                    productSkuReviewDTO.setProductSkuCode(productSkuCode);
                                    productSkuReviewDTO.setProductSkuPriceId(productSkuPrice.getId());
                                    productSkuReviewDTO.addField("msrp", NumberUtil.toStr(beforeMsrp), NumberUtil.toStr(msrp, "0"));
                                    productSkuReviewDTO.addField("dropShippingPrice", NumberUtil.toStr(beforeDropShippingPrice), NumberUtil.toStr(beforeDropShippingPrice, "0"));
                                    productSkuReviewDTO.addField("pickUpPrice", NumberUtil.toStr(beforePickUpPrice), NumberUtil.toStr(beforePickUpPrice, "0"));
                                    productSkuReviewDTO.addField("unitPrice", NumberUtil.toStr(beforeUnitPrice), NumberUtil.toStr(beforeUnitPrice, "0"));
                                    productSkuReviewDTO.addField("operationFee", NumberUtil.toStr(beforeOperationFee), NumberUtil.toStr(beforeOperationFee, "0"));
                                    productSkuReviewDTO.addField("finalDeliveryFee", NumberUtil.toStr(beforeFinalDeliveryFee), NumberUtil.toStr(beforeFinalDeliveryFee, "0"));
                                    productSkuReviewDTO.addField("ruleId", StrUtil.toString(1), StrUtil.toString(1));
                                    productSkuReviewList.add(productSkuReviewDTO);
                                }
                            }
                        }

                    }
                }
                productSku.setShelfState(skuShelfState);
                validSkuList.add(productSku);
            }// todo 草稿加价格其他的不能加和修改
//            iProductSkuPriceService.saveOrUpdateBatch(newProductSkuPriceList);
            // 设置SKU库存数量
            productSkuService.setProductSkuStock(validSkuList);
            iProductSkuService.saveOrUpdateBatch(validSkuList);
            if (ProductVerifyStateEnum.Rejected.equals(verifyState)) {
                product.setVerifyState(ProductVerifyStateEnum.Draft);
            }

            if (ProductVerifyStateEnum.Accepted.equals(verifyState)) {
                if (CollUtil.isNotEmpty(productSkuReviewList)) {
                    ProductReviewDTO productReviewDTO = new ProductReviewDTO();
                    productReviewDTO.setProductCode(productCode);
                    productReviewDTO.setSubmitTenantId(tenantId);
                    productReviewDTO.setChangeFields(SPUChangeFields);
                    productReviewDTO.setProductSkuReviewList(productSkuReviewList);
                    productReviewDTO.setReviewType(ProductReviewTypeEnum.Price);
                    productReviewDTO.setReviewStatus(ProductVerifyStateEnum.Pending);
                    productReviewRecordService.generateReviewRecord(productReviewDTO);
                    product.setHasPriceChange(true);
                    // 存在价格变更审核，支持的物流类型要还原会修改前，等待价格审核通过后，再一起生效
                    product.setSupportedLogistics(beforeSupportedLogistics);
                }

                if (CollUtil.isNotEmpty(newProductSkuReviewList)) {
                    ProductReviewDTO productReviewDTO = new ProductReviewDTO();
                    productReviewDTO.setProductCode(productCode);
                    productReviewDTO.setSubmitTenantId(tenantId);
                    productReviewDTO.setChangeFields(SPUChangeFields);
                    productReviewDTO.setProductSkuReviewList(newProductSkuReviewList);
                    productReviewDTO.setReviewType(ProductReviewTypeEnum.NewProductSku);
                    productReviewDTO.setReviewStatus(ProductVerifyStateEnum.Pending);
                    productReviewRecordService.generateReviewRecord(productReviewDTO);
                }
            }
            iProductService.updateById(product);
            esProductSupport.productUpload(product);
            return new ProductSaveVo(productCode, product.isHasPriceChange());
        } else {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.PRODUCT_NOT_EXIST);
        }
    }

    /**
     * 商品更新发货方式处理映射关系
     * @param product
     * @param newSupportedLogistics
     */
    private void dealSupportedLogisticByProductSkuCode(Product product,String newSupportedLogistics) {
        //根据产品查询商品
        List<ProductSkuListVo> productSkuListVos = TenantHelper.ignore(()->iProductSkuService.queryProductSkuListVoByProductId(product.getId()));
        List<String> productSkuCodeNormal = new ArrayList<>();
        List<String> productSkuCodeException = new ArrayList<>();
        if (CollUtil.isNotEmpty(productSkuListVos)){
            //根据商品查询映射
            productSkuListVos.forEach(s->{
                //根据productSkuCode查询商品
                LambdaQueryWrapper<ProductMapping> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(ProductMapping::getProductSkuCode,s.getProductSkuCode());
                List<ProductMapping> mappingList = TenantHelper.ignore(() -> iProductMappingService.getBaseMapper()
                                                                                              .selectList(wrapper));
                for (ProductMapping productMapping : mappingList) {
                    //查询渠道发货方式
                    TenantSalesChannel tenantSalesChannel =  TenantHelper.ignore(()->tenantSalesChannelService.getById(productMapping.getChannelId()));
                    if (ObjectUtil.isNull(tenantSalesChannel)){
                        throw new RuntimeException("渠道信息不存在！");
                    }
                    //判断发货方式是否一直
                    //判断渠道的的发货方式和商品新的的方式是否一直
                    boolean isSupport = SupportedLogisticsEnum.valueOf(newSupportedLogistics).allowShipping(tenantSalesChannel.getLogisticsType());
                    //如果不一致，更新映射关系
                    UpdateWrapper<ProductMapping> up = new UpdateWrapper<>();
                    up.eq("id",productMapping.getId());
                    up.set("update_time",new Date());
                    up.set("update_by",LoginHelper.getUserId());
                    //只有发货方式一致，且上架才能映射成功
                    if (isSupport && product.getShelfState().equals(ShelfStateEnum.OnShelf)){
                        productSkuCodeNormal.add(s.getProductSkuCode());
                        up.set("sync_state", SyncStateEnum.Mapped);
                    }else {
                        productSkuCodeException.add(s.getProductSkuCode());
                        up.set("sync_state", SyncStateEnum.Exceptions);
                    }
                    TenantHelper.ignore(()->iProductMappingService.getBaseMapper().update(null,up));
                }
            });
        }
        //通知订单模块
        try {
            if(CollUtil.isNotEmpty(productSkuCodeNormal)){
                productSupport.orderExceptionDisposeRecover(productSkuCodeNormal, OrderExceptionEnum.product_mapping_exception);
            }
            if(CollUtil.isNotEmpty(productSkuCodeException)){
                productSupport.orderExceptionDispose(productSkuCodeException,OrderExceptionEnum.product_mapping_exception);
            }
        }catch (Exception e){
            log.error(e.getMessage());
        }
    }

    /**
     * 商品上架/下架
     *
     * @param bo
     * @return
     */
    @Override
    @Transactional(rollbackFor = {Exception.class, RStatusCodeException.class})
    public R<Void> onOrOffShelfProduct(ProductShelfBo bo) throws RStatusCodeException {
        List<String> productCodeList = bo.getProductCodeList();
        String shelfState = bo.getShelfState();
        if (CollUtil.isEmpty(productCodeList) || StrUtil.isBlank(shelfState)) {
            R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }

        LoginUser loginUser = LoginHelper.getLoginUser(TenantType.Supplier);

        ShelfStateEnum shelfStateEnum = ShelfStateEnum.valueOf(shelfState);

        List<Product> productList = new ArrayList<>();
        List<ProductSku> productSkuList = new ArrayList<>();
        for (String productCode : productCodeList) {
            Product product = iProductService.queryByProductCodeNotDelete(productCode);

            if (product == null || ShelfStateEnum.ForcedOffShelf.equals(product.getShelfState())) {
                continue;
            }

            Long productId = product.getId();
            ProductVerifyStateEnum verifyState = product.getVerifyState();
            List<ProductSku> item_productSkuList = iProductSkuService.queryByProductIdNotDelete(productId, ProductVerifyStateEnum.Accepted);
            // 上架要验证审批状态是否通过等
            if (ShelfStateEnum.OnShelf.equals(shelfStateEnum)) {
                product.setLastOnShelfTime(new Date());

                if (product.getFirstOnShelfTime() == null) {
                    product.setFirstOnShelfTime(new Date());
                }

                if (ObjectUtil.notEqual(ProductVerifyStateEnum.Accepted, verifyState)) {
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.PRODUCT_NOT_REVIEW_CANNOT_ON_SHELF);
                }
                item_productSkuList.forEach(s->{
                    s.setShelfState(ShelfStateEnum.OnShelf);
                });
            } else {

                // 查询关联活动，如果有活动在进行，不允许下架
                if (CollUtil.isNotEmpty(item_productSkuList)) {
                    //已经提交审核和正在进行中的商家活动
                    ProductActivityStateEnum[] stateArray = new ProductActivityStateEnum[]{
                        ProductActivityStateEnum.UnderReview, ProductActivityStateEnum.Published,
                        ProductActivityStateEnum.InProgress, ProductActivityStateEnum.Canceling
                    };

//                    List<ProductActivity> productActivityList = iProductActivityService.queryByProductCodeAndActivityState(productCode, stateArray);
//                    log.info("【商品上架/下架】商品{}存在的未完成活动 => {}", productCode, JSONUtil.toJsonStr(productActivityList));
//                    if (CollUtil.isNotEmpty(productActivityList)) {
//                        List<String> productActivityCodes = productActivityList.stream().map(ProductActivity::getActivityCode).collect(Collectors.toList());
//                        throw new RStatusCodeException(ZSMallStatusCodeEnum.PRODUCT_OFF_SHELF_ERROR_ACTIVITY_NOT_END.args(CollUtil.join(productActivityCodes, ", ")));
//                    }

                    // 查询分销商还在进行中的活动
//                    List<ProductActivityItem> activityItem = iProductActivityItemService.queryInProgressAndParentCanceled(productCode);
//                    if (CollUtil.isNotEmpty(activityItem)) {
//                        List<String> activityCodeParentList = activityItem.stream().map(ProductActivityItem::getActivityCodeParent).collect(Collectors.toList());
//                        // 多个分销商有未结束活动关联该商品，需要提示
//                        throw new RStatusCodeException(ZSMallStatusCodeEnum.PRODUCT_OFF_SHELF_ERROR_ACTIVITY_ITEM_NOT_END.args(CollUtil.join(activityCodeParentList, ", ")));
//                    }
                }
                //暂时没有活动相关功能，所以忽略上面的代码
                item_productSkuList.forEach(s->{
                    s.setShelfState(ShelfStateEnum.OffShelf);
                });
            }

            product.setShelfState(shelfStateEnum);
            productList.add(product);
            if (CollUtil.isNotEmpty(item_productSkuList)) {
                productSkuList.addAll(item_productSkuList);
            }
        }

        iProductService.updateBatchById(productList);
        iProductSkuService.updateBatchById(productSkuList);
        // 处理商品映射
        Map<Long, Product> productMap = productList.stream()
                                                   .collect(Collectors.toMap(Product::getId, Function.identity()));
        List<String> productCodeOrderList = productList.stream().map(Product::getProductCode).collect(Collectors.toList());
        if(CollUtil.isNotEmpty(productCodeOrderList)) {
            List<ProductSku> productSkuOrderList = iProductSkuService.queryListByProductCodes(productCodeOrderList);
            //查询映射
            for (ProductSku productSku : productSkuOrderList) {
                //查询映射
                //根据productSkuCode查询商品
                LambdaQueryWrapper<ProductMapping> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(ProductMapping::getProductSkuCode, productSku.getProductSkuCode());
                List<ProductMapping> mappingList = TenantHelper.ignore(() -> iProductMappingService.getBaseMapper()
                                                                                                   .selectList(wrapper));
                for (ProductMapping productMapping : mappingList) {
                    //查询渠道发货方式
                    TenantSalesChannel tenantSalesChannel = TenantHelper.ignore(() -> tenantSalesChannelService.getById(productMapping.getChannelId()));
                    Product product = productMap.get(productSku.getProductId());
                    if (ObjectUtil.isNull(product)){
                        throw  new RuntimeException(String.format("未获取到产品信息，产品id:%d"+productSku.getProductId()));
                    }
                    //判断渠道的的发货方式和商品新的的方式是否一直
                    boolean isSupport = SupportedLogisticsEnum.valueOf(product.getSupportedLogistics().name())
                                                              .allowShipping(tenantSalesChannel.getLogisticsType());
                    UpdateWrapper<ProductMapping> up = new UpdateWrapper<>();
                    up.eq("id", productMapping.getId());
                    up.set("update_time", new Date());
                    up.set("update_by", LoginHelper.getUserId());
                    //如果是上架且发货方式一致，更新映射成功，否则映射异常
                    if (isSupport && product.getShelfState().equals(ShelfStateEnum.OnShelf)){
                        up.set("sync_state", SyncStateEnum.Mapped);
                    } else {
                        up.set("sync_state", SyncStateEnum.Exceptions);
                    }
                    TenantHelper.ignore(() -> iProductMappingService.getBaseMapper().update(null, up));
                }
            }
        }

        // 订单异常状态处理
        List<String> productSkuCodeOrderList = new ArrayList<>();
        List<String> productCodeOrderExceptionList = productList.stream().map(Product::getProductCode).collect(Collectors.toList());
        if(CollUtil.isNotEmpty(productCodeOrderExceptionList)) {
            List<ProductSku> productSkuOrderList = iProductSkuService.queryListByProductCodes(productCodeOrderExceptionList);
            if (CollUtil.isNotEmpty(productSkuOrderList)) {
                productSkuCodeOrderList = productSkuOrderList.stream().map(ProductSku::getProductSkuCode)
                                                             .collect(Collectors.toList());
            }
        }
        if(shelfState.equals(ShelfStateEnum.OnShelf.name())){
            if (CollUtil.isNotEmpty(productSkuCodeOrderList)) {
                // 商品上架，订单异常状态恢复，订单异常状态恢复为正常状态，如果有支付错误信息修改为null
                productSupport.orderExceptionDisposeRecover(productSkuCodeOrderList, OrderExceptionEnum.product_mapping_exception);
            }
        } else {
            if (CollUtil.isNotEmpty(productSkuCodeOrderList)) {
                // 商品下架 订单异常状态处理
                productSupport.orderExceptionDispose(productSkuCodeOrderList, OrderExceptionEnum.product_mapping_exception);
            }
            // 商品下架，将产品映射状态修改为映射正常
//            iProductMappingService.updateProductMappingRecover(productSkuCodeOrderList, SyncStateEnum.Exceptions);
        }
        // 保存主商品后，开始调整每个SKU的库存，并创建库存同步任务
        productSkuService.setProductSkuStock(productSkuList);
        return R.ok();
    }

    /**
     * 删除商品
     *
     * @param productDeleteBo
     * @return
     * @throws RStatusCodeException
     */
    @Override
    @Transactional(rollbackFor = {Exception.class, RStatusCodeException.class})
    public Boolean deleteProduct(ProductDeleteBo productDeleteBo) throws Exception {
        List<String> productCodeList = productDeleteBo.getProductCodeList();
        if (CollUtil.isEmpty(productCodeList)) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }

//        LoginUser loginUser = LoginHelper.getLoginUser(TenantType.Supplier);


        for (String productCode : productCodeList) {
            Product product = iProductService.queryByProductCodeNotDelete(productCode);
            if (product != null) {
                Long productId = product.getId();
                ProductTypeEnum productType = product.getProductType();
                List<ProductSku> productSkuList = iProductSkuService.queryByProductIdNotDelete(productId);
                /**
                 * 批发商品删除时先判断是否存在进行中的意向单，存在 则不能删除
                 */
                if (productType.equals(ProductTypeEnum.WholesaleProduct)) {
                    List<String> itemNoList = productSkuList.stream().map(ProductSku::getProductSkuCode).collect(Collectors.toList());
                    List<WholesaleIntentionOrderItem> wholesaleIntentionOrderItems = iWholesaleIntentionOrderItemService.existsProcessingOrderItem(itemNoList);
                    if (CollUtil.isNotEmpty(wholesaleIntentionOrderItems)) {
                        List<String> collect = wholesaleIntentionOrderItems.stream().map(WholesaleIntentionOrderItem::getProductSkuCode).distinct().collect(Collectors.toList());
                        throw new RStatusCodeException(ZSMallStatusCodeEnum.HAS_INTENTION_NOT_DEL_ERROR.args(collect));
                    }
                }

                List<Long> deleteSkuId = productSkuList.stream().map(ProductSku::getId).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(deleteSkuId)) {
                    iProductSkuService.deleteWithValidByIds(deleteSkuId, false);
                    // iProductSkuPriceService.deleteByProductSkuIdList(deleteSkuId);
                    // iProductSkuDetailService.deleteByProductSkuIdList(deleteSkuId);
                    iProductSkuStockService.deleteByProductSkuIdList(deleteSkuId);
                    iProductSkuAttributeService.deleteByProductSkuIdList(deleteSkuId);
                    // iProductSkuAttachmentService.deleteByProductSkuIdList(deleteSkuId);
                    iProductSkuPriceRuleRelationService.deleteByProductSkuIdList(deleteSkuId);
                }
                iProductAttributeService.deleteByProductId(productId);
                iProductCategoryRelationService.deleteByProductId(productId);
                iProductReviewRecordService.deletePendingByProductCode(productCode);
                iProductReviewChangeDetailService.deleteByProductCode(productCode);
                iProductService.removeById(productId);
            } else {
                throw new RStatusCodeException(ZSMallStatusCodeEnum.PRODUCT_NOT_EXIST);
            }
        }
        return true;
    }

    /**
     * 删除指定渠道商品
     *
     * @param channelId
     * @return
     */
    @Transactional
    @Override
    public boolean deleteProductByChannelId(Long channelId) {
        try {
            if (channelId != null) {
                List<ProductMapping> productMappingList = iProductMappingService.queryByChannelIdNotDelete(channelId);
                if (CollUtil.isNotEmpty(productMappingList)) {
                    iProductMappingService.removeBatchByIds(productMappingList);
                }
            }
            return true;
        } catch (Exception e) {
            log.error("deleteProductByChannelId error. message = {}", e.getMessage(), e);
            return false;
        }
    }


    @InMethodLog(value = "翻页查询商品价格修改列表")
    @Override
    public TableDataInfo<ProductPriceVo> getProductPriceChange(ProductPriceBo bo, PageQuery pageQuery) {

//        if(!TenantConstants.DEFAULT_TENANT_ID.equals(LoginHelper.getTenantId())){
//            LoginUser loginUser = LoginHelper.getLoginUser(TenantType.Supplier);
//        }
        String queryType = bo.getQueryType();
        String queryValue = StrUtil.isBlank(bo.getQueryValue()) ? null : StrUtil.trim(bo.getQueryValue());
        Long siteId = bo.getSiteId();
        Integer pageSize = pageQuery.getPageSize();
        Integer pageNum = pageQuery.getPageNum();

        if (StrUtil.isBlank(queryType) || StrUtil.isBlank(queryValue)) {
            queryType = null;
            queryValue = null;
        }
        // 审核状态
        String skuShelfState = bo.getSkuShelfState();
        String skuAuditStatus = bo.getSkuAuditStatus();

        Page<ProductSku> page = new Page<>(pageNum, pageSize);
        IPage<ProductSku> productSkuPage = iProductSkuService.getPage(queryType, queryValue, page,siteId,skuShelfState,skuAuditStatus);
        List<ProductSku> records = productSkuPage.getRecords();
        Map<Long, SiteCountryCurrency> siteIdMap = iSiteCountryCurrencyService.getSiteIdMap();
        List<ProductPriceVo> voList = new ArrayList<>();
        for (ProductSku productSku : records) {
            Long id = productSku.getId();
            String productSkuCode = productSku.getProductSkuCode();
            String sku = productSku.getSku();
            Product product = iProductService.queryByProductCode(productSku.getProductCode());
            String name = product.getName();
            SupportedLogisticsEnum supportedLogisticsEnum = product.getSupportedLogistics();
            ProductSkuAttachmentVo skuAttachmentVo = iProductSkuAttachmentService.queryFirstImageByProductSkuId(productSku.getId());
            // todo 先统计,要改动
            LambdaQueryWrapper<ProductSkuPrice> wrapper = new LambdaQueryWrapper<ProductSkuPrice>()
                .eq(ProductSkuPrice::getProductSkuId, id)
                .eq(ObjectUtil.isNotEmpty(siteId),ProductSkuPrice::getSiteId,siteId)
                .eq(ProductSkuPrice::getDelFlag,0);
            List<ProductSkuPrice> productSkuPrices = iProductSkuPriceService.list(wrapper);
            log.info("productSkuPrice = {}", JSONUtil.toJsonStr(productSkuPrices));

            String supportedLogistics = null;
            if (ObjectUtil.equals(supportedLogisticsEnum, SupportedLogisticsEnum.PickUpOnly)) {
                supportedLogistics = "Pick Up";
            }
            if (ObjectUtil.equals(supportedLogisticsEnum, SupportedLogisticsEnum.DropShippingOnly)) {
                supportedLogistics = "Drop Shipping";
            }
            if (ObjectUtil.equals(supportedLogisticsEnum, SupportedLogisticsEnum.All)) {
                supportedLogistics = "Pick Up & Drop Shipping";
            }

            //查询审核记录，商品价格是否处于审核中
            Boolean exists = iProductReviewRecordService.existsByProductSkuCode(productSkuCode, ProductReviewTypeEnum.Price,
                ProductVerifyStateEnum.Pending);
            ProductPriceVo productPriceVo = new ProductPriceVo();
            ProductPriceVo.Product productVo = new ProductPriceVo.Product();

            if(ObjectUtil.isNotEmpty(skuAttachmentVo)){
                String attachmentShowUrl = skuAttachmentVo.getAttachmentShowUrl();
                productVo.setProductImg(attachmentShowUrl);
            }

            productVo.setItemNo(productSkuCode);
            productVo.setProductName(name);
            productVo.setSku(sku);
            productVo.setSupportedLogistics(supportedLogistics);
            productVo.setSupportedLogisticsMethod(supportedLogisticsEnum.name());
            productVo.setHasPriceChange(exists);
            // 如果未上架 ForcedOffShelf+Pending 不允许改价格,管理员审批中
            ProductVerifyStateEnum verifyState = productSku.getVerifyState();
            ShelfStateEnum shelfState = productSku.getShelfState();
            if(ProductVerifyStateEnum.Pending.equals(verifyState) && ShelfStateEnum.ForcedOffShelf.equals(shelfState)){
                productVo.setHasPriceChange(true);
            }
            List<ProductPriceVo.SitePrice> sitePrices = new ArrayList<>();
            // 已是sku维度,需要分站点维度
            for (ProductSkuPrice productSkuPrice : productSkuPrices) {
                if (ObjectUtil.isEmpty(productSkuPrice)){
                    continue;
                }
                SiteCountryCurrency currency = siteIdMap.get(productSkuPrice.getSiteId());
                ProductPriceVo.SitePrice sitePrice = new ProductPriceVo.SitePrice();
                ProductPriceVo.OriginPrice originPrice = new ProductPriceVo.OriginPrice();

                BigDecimal originalUnitPrice = BigDecimal.ZERO;
                BigDecimal originalOperationFee = BigDecimal.ZERO;
                BigDecimal originalFinalDeliveryFee = BigDecimal.ZERO;
                BigDecimal msrp = BigDecimal.ZERO;
                if (productSkuPrice != null) {
                    originalUnitPrice = productSkuPrice.getOriginalUnitPrice();
                    originalOperationFee = productSkuPrice.getOriginalOperationFee();
                    originalFinalDeliveryFee = productSkuPrice.getOriginalFinalDeliveryFee();
                    msrp = productSkuPrice.getMsrp();
                }


                originPrice.setOriginalUnitPrice(originalUnitPrice);
                originPrice.setOriginalOperationFee(originalOperationFee);
                originPrice.setOriginalFinalDeliveryFee(originalFinalDeliveryFee);
                originPrice.setMsrp(msrp);

                sitePrice.setSiteId(currency.getId());
                sitePrice.setCurrencySymbol(currency.getCurrencySymbol());
                sitePrice.setId(productSkuPrice.getId());
                sitePrice.setCountryCode(currency.getCountryCode());
                sitePrice.setCurrencySymbol(currency.getCurrencySymbol());
                sitePrice.setCurrencyCode(currency.getCurrencyCode());
                sitePrice.setOriginPrice(originPrice);
                sitePrice.getSetSitePrice().setItemNo(productSkuCode);
                // 如果存在修改记录,将修改的金额放入

                if(exists){
                    // 商品+站点得到价格组合 +审核中表主键
                    Long reviewRecordId = iProductReviewRecordService.getIdByProductSkuCode(productSkuCode, ProductReviewTypeEnum.Price,
                        ProductVerifyStateEnum.Pending);

                    LambdaQueryWrapper<ProductReviewChangeDetail> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(ProductReviewChangeDetail::getProductSkuPriceId, productSkuPrice.getId());
                    queryWrapper.eq(ProductReviewChangeDetail::getSiteId, productSkuPrice.getSiteId());
                    queryWrapper.eq(ProductReviewChangeDetail::getProductSkuPriceId,productSkuPrice.getId());
                    queryWrapper.eq(ProductReviewChangeDetail::getReviewRecordId,reviewRecordId);

                    List<ProductReviewChangeDetail> changeDetails = iProductReviewChangeDetailService.list(queryWrapper);
                    getModifyPriceAfter(changeDetails, sitePrice);

                }

                sitePrices.add(sitePrice);

            }
            productPriceVo.setProduct(productVo);
            productPriceVo.addSitePrice(sitePrices);

            productPriceVo.setItemNo(productVo.getItemNo());
            productPriceVo.setSkuShelfState(shelfState.equals(ShelfStateEnum.OnShelf)
                ? productSku.getShelfState().name() : shelfState.name());

            productPriceVo.setSkuAuditStatus(productSku.getVerifyState().name());
            voList.add(productPriceVo);

        }
        return TableDataInfo.build(voList, productSkuPage.getTotal());
    }

    /**
     * 功能描述：获取修改后价格
     *
     * @param changeDetails 更改详细信息
     * @param sitePrice     场地价格
     * <AUTHOR>
     * @date 2025/01/26
     */
    private static void getModifyPriceAfter(List<ProductReviewChangeDetail> changeDetails, ProductPriceVo.SitePrice sitePrice) {
        if (CollUtil.isNotEmpty(changeDetails)) {
            for (ProductReviewChangeDetail changeDetail : changeDetails) {
                String fieldName = changeDetail.getFieldName();
                if(FiledTypeEnum.unitPrice.name().equals(fieldName)){
                    String fieldValueAfter = changeDetail.getFieldValueAfter();
                    if(ObjectUtil.isNotEmpty(fieldValueAfter)){
                        sitePrice.getSetSitePrice().setOriginalUnitPrice(new BigDecimal(fieldValueAfter));
                        sitePrice.getSetSitePrice().setIsDataChange(true);
                    }

                }
                if(FiledTypeEnum.operationFee.name().equals(fieldName)){
                    String fieldValueAfter = changeDetail.getFieldValueAfter();
                    if(ObjectUtil.isNotEmpty(fieldValueAfter)){
                        sitePrice.getSetSitePrice().setOriginalOperationFee(new BigDecimal(fieldValueAfter));
                        sitePrice.getSetSitePrice().setIsDataChange(true);
                    }

                }
                if(FiledTypeEnum.finalDeliveryFee.name().equals(fieldName)){
                    String fieldValueAfter = changeDetail.getFieldValueAfter();
                    if(ObjectUtil.isNotEmpty(fieldValueAfter)){
                        sitePrice.getSetSitePrice().setOriginalFinalDeliveryFee(new BigDecimal(fieldValueAfter));
                        sitePrice.getSetSitePrice().setIsDataChange(true);
                    }

                }
            }

        }
    }

    @InMethodLog(value = "修改价格")
    @Override
    @Transactional(rollbackFor = {RStatusCodeException.class, Exception.class})
    public R<Void> changeProductPrice(PriceFormListBySupBo bo) throws RStatusCodeException {

        List<PriceFormListBySupBo.PriceFormBySupBo> priceList = bo.getPriceList();
        if (CollUtil.isEmpty(priceList)) {
            return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }
        Map<Long, SiteCountryCurrency> siteIdMap = iSiteCountryCurrencyService.getSiteIdMap();
        for (PriceFormListBySupBo.PriceFormBySupBo priceForm : priceList) {
            String itemNo = priceForm.getItemNo();
            Long siteId = priceForm.getSiteId();
            Long id = priceForm.getId();
            if (StrUtil.isBlank(itemNo)) {
                return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
            }

            BigDecimal originalUnitPrice = priceForm.getOriginalUnitPrice();
            BigDecimal originalOperationFee = priceForm.getOriginalOperationFee();
            BigDecimal originalFinalDeliveryFee = priceForm.getOriginalFinalDeliveryFee();
            BigDecimal msrp = priceForm.getMsrp();

            ProductSku productSku = iProductSkuService.queryByProductSkuCode(itemNo);
            if (productSku == null) {
                return R.fail(ZSMallStatusCodeEnum.PRODUCT_SKU_NOT_EXIST);
            }

            ProductSkuPrice oldProductSkuPrice = iProductSkuPriceService.getById(id);
            ProductSkuPrice newProductSkuPrice = ObjectUtil.clone(oldProductSkuPrice);
            //价格取两位小数，四舍五入 如果填写了数值就更新
            if (originalUnitPrice == null) {
                originalUnitPrice = oldProductSkuPrice.getOriginalUnitPrice();
            }
            if (originalOperationFee == null) {
                originalOperationFee = oldProductSkuPrice.getOriginalOperationFee();
            }
            if (originalFinalDeliveryFee == null) {
                originalFinalDeliveryFee = oldProductSkuPrice.getOriginalFinalDeliveryFee();
            }
            if (msrp == null) {
                msrp = oldProductSkuPrice.getMsrp();
            }
            newProductSkuPrice.setOriginalUnitPrice(originalUnitPrice.setScale(2, RoundingMode.HALF_UP));
            newProductSkuPrice.setOriginalOperationFee(originalOperationFee.setScale(2, RoundingMode.HALF_UP));
            newProductSkuPrice.setOriginalFinalDeliveryFee(originalFinalDeliveryFee.setScale(2, RoundingMode.HALF_UP));
            newProductSkuPrice.setMsrp(msrp.setScale(2, RoundingMode.HALF_UP));
            log.info("oldProductSkuPrice = {}", JSONUtil.toJsonStr(oldProductSkuPrice));
            log.info("newProductSkuPrice = {}", JSONUtil.toJsonStr(newProductSkuPrice));
            //对比价格，全部相等则不需要进行价格变更
            Boolean priceEquals = this.comparePrice(oldProductSkuPrice, newProductSkuPrice);
            if (!priceEquals) {
                //提交审核或者直接更新价格
                generateReviewRecordOrUpdatePrice(productSku, newProductSkuPrice,siteId,siteIdMap);
            }
        }

        return R.ok();
    }

    /**
     * 分页查询商品导入记录
     *
     * @param pageQuery
     * @return
     */
    @Override
    public TableDataInfo<ProductImportRecordVo> queryProductImportRecordPage(PageQuery pageQuery) {
        return iProductImportRecordService.queryPageList(pageQuery);
    }

    /**
     * 上传商品Excel
     *
     * @param file
     * @return
     * @throws Exception
     */
    @Override
    @Deprecated
    public R<Void> uploadProductExcel(MultipartFile file) throws Exception {
       LoginUser loginUser = LoginHelper.getLoginUser(TenantType.Supplier);
        String tenantId = loginUser.getTenantId();
//        String tenantId = "SJN1857";
        if (file == null) {
            return R.fail(ZSMallStatusCodeEnum.UPLOAD_FILE_IS_EMPTY);
        }

        Boolean existsed = iProductImportRecordService.existImportingRecord();
        if (existsed) {
            return R.fail(ZSMallStatusCodeEnum.PRODUCT_IMPORT_FILE_EXIST_IMPORTING);
        }

        InputStream inputStream = file.getInputStream();
        ExcelReader reader = ExcelUtil.getReader(inputStream);

        if (reader != null) {
            Sheet sheet = reader.getSheet();
            String sheetName = sheet.getSheetName();
            log.info("上传商品Excel - sheetName = {}", sheetName);
            int columnCount = reader.getColumnCount();
            log.info("上传商品Excel - columnCount = {}", columnCount);
            if (columnCount != 56) {
                return R.fail(ZSMallStatusCodeEnum.EXCEL_COLUMN_COUNT_NOT_MATCH);
            }
        }

        // 初始化信息构建者
        ExcelMsgBuilder<ProductImportDTO> builder = ZExcelUtil.msgBuilder(reader, 1, ProductImportDTO.class);
        builder.setMsgPrefix("<p>").setMsgSuffix("</p></br>");

        ProductImportRecord record = new ProductImportRecord();
        record.setImportRecordNo(productCodeGenerator.codeGenerate(BusinessCodeEnum.ProductImportRecordNo));
        record.setImportFileName(file.getOriginalFilename());
        record.setImportState(ImportStateEnum.Importing);
        iProductImportRecordService.save(record);

        // 开启线程导入
        ThreadUtil.execute(() -> {
            String importRecordNo = record.getImportRecordNo();
            Long recordId = record.getId();
            List<ProductImportDTO> dtoList;
            try {
                dtoList = ZExcelUtil.parseFieldDTO(reader, ProductImportDTO.class, 1, 10);
                if (CollUtil.isEmpty(dtoList)) {
                    throw new ExcelMessageException(builder.build(ExcelMessageEnum.NOT_VALID_ROW));
                }
            } catch (ExcelMessageException e) {
                record.setImportMessage(e.getLocaleMessage().toJSON());
                record.setImportState(ImportStateEnum.Failed);
                iProductImportRecordService.updateById(record);
                return;
            }

            LocaleMessage globalMessage = new LocaleMessage();
            // 全局Sku查重
            List<String> globalSku = new ArrayList<>();
            // 全局Upc查重
            List<String> globalUpc = new ArrayList<>();

            Map<String, ProductImportBo> productGroupMap = new HashMap<>();
            // 导入记录完成
            // 保存产品相关信息
            for (ProductImportDTO dto : dtoList) {
                // 每次循环开始设置当前的行号，以保证构建信息时展示的行号是正确的
                int showRowIndex = dto.getShowRowIndex();
                builder.setNowShowRow(showRowIndex);

                try {
                    String group = dto.getGroup();
                    ProductImportBo product = productGroupMap.get(group);
                    List<ProductAttribute> productAttributeList;

                    List<ProductSkuImportBo> productSkuList;
                    List<ProductGlobalAttribute> globalAttributeList;
                    List<ProductAttribute> optionalSpecList;
                    if (product == null) {
                        product = new ProductImportBo();

                        String category = dto.getCategory();
                        String productName = dto.getProductName();
                        String supportedLogistics = dto.getSupportedLogistics();
                        String forbiddenChannel = dto.getForbiddenChannel();
                        String description = dto.getDescription();

                        if (StrUtil.isBlank(category)) {
                            throw new ExcelMessageException(builder.build(ProductImportDTO::getCategory, ExcelMessageEnum.REQUIRE));
                        }
                        if (StrUtil.isBlank(productName)) {
                            throw new ExcelMessageException(builder.build(ProductImportDTO::getProductName, ExcelMessageEnum.REQUIRE));
                        }
                        if (StrUtil.isBlank(supportedLogistics)) {
                            throw new ExcelMessageException(builder.build(ProductImportDTO::getSupportedLogistics, ExcelMessageEnum.REQUIRE));
                        }

                        if (productName.length() > 150) {
                            throw new ExcelMessageException(builder.build(ProductImportDTO::getProductName, ExcelMessageEnum.PRODUCT_NAME_LIMIT));
                        }

                        SupportedLogisticsEnum supportedLogisticsEnum;
                        // 支持的物流类型
                        if (StrUtil.equals(supportedLogistics, "Pick Up & Dropshipping")) {
                            supportedLogisticsEnum = SupportedLogisticsEnum.All;
                        } else if (StrUtil.equals(supportedLogistics, "Pick Up Only")) {
                            supportedLogisticsEnum = SupportedLogisticsEnum.PickUpOnly;
                        } else if (StrUtil.equals(supportedLogistics, "Dropshipping Only")) {
                            supportedLogisticsEnum = SupportedLogisticsEnum.DropShippingOnly;
                        } else {
                            throw new ExcelMessageException(builder.build(ProductImportDTO::getSupportedLogistics, ExcelMessageEnum.ILLEGAL_ARGUMENT));
                        }

                        // 禁售渠道
                        JSONArray forbiddenChannelArray = new JSONArray();
                        if (StrUtil.isNotBlank(forbiddenChannel)) {
                            forbiddenChannel = StrUtil.replace(forbiddenChannel, "；", ";");
                            List<String> channelList = StrUtil.split(forbiddenChannel, ";");
                            for (String channel : channelList) {
                                try {
                                    ChannelTypeEnum channelType = ChannelTypeEnum.valueOf(channel);
                                    forbiddenChannelArray.add(channelType.name());
                                } catch (Exception e) {
                                }
                            }
                        }

//                        if (!forbiddenChannelArray.contains(ChannelTypeEnum.Wayfair.name())) {
//                            forbiddenChannelArray.add(ChannelTypeEnum.Wayfair.name());
//                        }

                        // 分类处理
                        String finalCateoryName;
                        if (StrUtil.contains(category, "/")) {
                            List<String> categoryList = StrUtil.split(category, "/");
                            finalCateoryName = CollUtil.getLast(categoryList);
                        } else {
                            finalCateoryName = category;
                        }

                        List<ProductCategory> productCategoryList = iProductCategoryService.queryByCategoryName(category);
                        if (CollUtil.isEmpty(productCategoryList)) {
                            throw new ExcelMessageException(builder.build(ProductImportDTO::getCategory, ExcelMessageEnum.PRODUCT_CATEGORY_NOT_FOUND));
                        }
                        ProductCategory productCategory = productCategoryList.get(0);
                        Long belongCategoryId = productCategory.getId();

                        // 处理分类关系
                        List<ProductCategory> productCategoryTree = iProductCategoryService.queryCategoryChainById(belongCategoryId);
                        List<ProductCategoryRelation> categoryRelationList = new ArrayList<>();
                        if (CollUtil.isNotEmpty(productCategoryList)) {
                            for (ProductCategory one_productCategoryTree : productCategoryTree) {
                                ProductCategoryRelation pcr = new ProductCategoryRelation();
                                pcr.setProductCategoryId(one_productCategoryTree.getId());
                                categoryRelationList.add(pcr);
                            }
                        }

                        String productCode = productCodeGenerator.codeGenerate(BusinessCodeEnum.ProductCode);
                        product.setName(productName);
                        product.setBelongCategoryId(belongCategoryId);
                        product.setSupportedLogistics(supportedLogisticsEnum);
                        product.setDescription(StrUtil.emptyToNull(description));
                        product.setProductCode(productCode);
                        product.setProductType(ProductTypeEnum.NormalProduct);
                        product.setForbiddenChannel(forbiddenChannelArray);
                        product.setShelfState(ShelfStateEnum.ForcedOffShelf);
                        product.setVerifyState(ProductVerifyStateEnum.Draft);
                        product.setDownloadCount(0);
                        product.setCategoryRelationList(categoryRelationList);

                        productAttributeList = new ArrayList<>();
                        // 处理商品特色 和 通用规格
                        // 查询是否员工设置了该分类的必填的通用规格，有的话也要加入到待保存数组中
                        List<ProductGlobalAttributeSimpleVo> requiredGlobalAttributeList = iProductGlobalAttributeService.queryByProductCategoryIdAndRequired(product.getBelongCategoryId(), AttributeBelongEnum.Platform.getValue(),
                            AttributeScopeEnum.GenericSpec.getValue(), true);
                        if (CollUtil.isNotEmpty(requiredGlobalAttributeList)) {
                            for (int i = 0; i < requiredGlobalAttributeList.size(); i++) {
                                ProductGlobalAttributeSimpleVo attributeSimpleVo = requiredGlobalAttributeList.get(i);
                                ProductAttribute productAttribute = productSupport.setProductAttribute(null, null, attributeSimpleVo.getAttributeName(), null, AttributeTypeEnum.GenericSpec, attributeSimpleVo.getId(), i);
                                productAttributeList.add(productAttribute);
                            }
                        }

                        int genericSpecSort = CollUtil.size(productAttributeList);
                        for (int i = 1; i <= 5; i++) {
                            Object featureName = ReflectUtil.getFieldValue(dto, "featureName" + i);
                            Object featureValue = ReflectUtil.getFieldValue(dto, "featureValue" + i);
                            if (ObjectUtil.isAllNotEmpty(featureName, featureValue)) {
                                ProductAttribute productAttribute = productSupport.setProductAttribute(null, null, featureName.toString(), featureValue.toString(), AttributeTypeEnum.Feature, null, i - 1);
                                productAttributeList.add(productAttribute);
                            }

                            Object genericSpecName = ReflectUtil.getFieldValue(dto, "genericSpecName" + i);
                            Object genericSpecValue = ReflectUtil.getFieldValue(dto, "genericSpecValue" + i);
                            if (ObjectUtil.isAllNotEmpty(genericSpecName, genericSpecValue)) {
                                ProductAttribute productAttribute = productSupport.setProductAttribute(null, null, genericSpecName.toString(), genericSpecValue.toString(), AttributeTypeEnum.GenericSpec, null, genericSpecSort + i);
                                productAttributeList.add(productAttribute);
                            }
                        }

                        productSkuList = new ArrayList<>();
                        globalAttributeList = new ArrayList<>();
                        optionalSpecList = new ArrayList<>();

                        // 处理可选规格
                        String optionalSpecName1 = StrUtil.trim(dto.getOptionalSpecName1());
                        if (StrUtil.isBlank(optionalSpecName1)) {
                            throw new ExcelMessageException(builder.build(ProductImportDTO::getOptionalSpecName1, ExcelMessageEnum.REQUIRE));
                        }

                        String optionalSpecName2 = StrUtil.trim(dto.getOptionalSpecName2());

                        ProductGlobalAttribute productGlobalAttribute1 = iProductGlobalAttributeService.queryByAttributeNameAndScopeAndCategory(tenantId, optionalSpecName1,
                            AttributeScopeEnum.OptionalSpec, product.getBelongCategoryId());
                        if (productGlobalAttribute1 == null) {
                            throw new ExcelMessageException(builder.build(ProductImportDTO::getOptionalSpecName1, ExcelMessageEnum.PRODUCT_VARIANT_DIMENSION_NOT_FOUND));
                        }
                        ProductAttribute productAttribute1 = productSupport.setProductAttribute(null, null, optionalSpecName1, null, AttributeTypeEnum.OptionalSpec, productGlobalAttribute1.getId(), 0);
                        globalAttributeList.add(productGlobalAttribute1);
                        optionalSpecList.add(productAttribute1);

                        if (StrUtil.isNotBlank(optionalSpecName2)) {
                            // 重复的变体维度
                            if (StrUtil.equals(optionalSpecName1, optionalSpecName2)) {
                                throw new ExcelMessageException(builder.build(ProductImportDTO::getOptionalSpecName2, ExcelMessageEnum.DUPLICATE_VARIANT));
                            }

                            ProductGlobalAttribute productGlobalAttribute2 = iProductGlobalAttributeService.queryByAttributeNameAndScopeAndCategory(tenantId, optionalSpecName2,
                                AttributeScopeEnum.OptionalSpec, product.getBelongCategoryId());
                            if (productGlobalAttribute2 == null) {
                                throw new ExcelMessageException(builder.build(ProductImportDTO::getOptionalSpecName2, ExcelMessageEnum.PRODUCT_VARIANT_DIMENSION_NOT_FOUND));
                            }
                            ProductAttribute productAttribute2 = productSupport.setProductAttribute(null, null, optionalSpecName2, null, AttributeTypeEnum.OptionalSpec, productGlobalAttribute2.getId(), 0);
                            globalAttributeList.add(productGlobalAttribute2);
                            optionalSpecList.add(productAttribute2);
                        }
                    } else {
                        productAttributeList = product.getProductAttributeList();
                        optionalSpecList = product.getOptionalSpecList();
                        globalAttributeList = product.getGlobalAttributeList();
                        productSkuList = product.getProductSkuVoList();
                    }
                    String productName = product.getName();
                    String productCode = product.getProductCode();
                    String description = product.getDescription();
                    Long belongCategoryId = product.getBelongCategoryId();
                    product.setProductAttributeList(productAttributeList);

                    String sku = dto.getSku();
                    String erpSku = dto.getErpSku();
                    String upc = dto.getUpc();
                    String optionalSpecValue1 = StrUtil.trim(dto.getOptionalSpecValue1());
                    String optionalSpecValue2 = StrUtil.trim(dto.getOptionalSpecValue2());

                    boolean existSku = iProductSkuService.existSku(sku, tenantId, null);
                    if (existSku || globalSku.contains(sku)) {
                        throw new ExcelMessageException(builder.build(ProductImportDTO::getSku, ExcelMessageEnum.SKU_REPEAT));
                    } else {
                        globalSku.add(sku);
                    }

                    if (StrUtil.isNotBlank(upc)) {
                        boolean existUpc = iProductSkuService.existUpc(upc, null);
                        if (existUpc || globalUpc.contains(upc)) {
                            throw new ExcelMessageException(builder.build(ProductImportDTO::getUpc, ExcelMessageEnum.UPC_REPEAT));
                        } else {
                            globalUpc.add(upc);
                        }
                    }

                    List<String> specComposeNameList = new ArrayList<>();
                    List<String> specValNameList = new ArrayList<>();
                    specValNameList.add(optionalSpecValue1);
                    specValNameList.add(optionalSpecValue2);
                    String specValName = CollUtil.join(CollUtil.removeEmpty(specValNameList), "/");
                    Boolean containsSpecValName = product.containsSpecValName(specValName);
                    if (containsSpecValName) {
                        throw new ExcelMessageException(builder.buildOnlyRow(ExcelMessageEnum.DUPLICATE_VARIANT_VALUE_COMBINATION));
                    }

                    List<ProductSkuAttribute> skuAttributeList = new ArrayList<>();
                    ProductGlobalAttribute productGlobalAttribute1 = CollUtil.get(globalAttributeList, 0);
                    if (productGlobalAttribute1 != null) {
                        Boolean isSupportCustom = productGlobalAttribute1.getIsSupportCustom();
                        // 不支持自定义属性值
                        if (!isSupportCustom) {
                            JSONArray attributeValues = productGlobalAttribute1.getAttributeValues();
                            // 且当前填写的属性值又不存在规定的值数组内，报错提示
                            if (!attributeValues.contains(optionalSpecValue1)) {
                                throw new ExcelMessageException(builder.build(ProductImportDTO::getOptionalSpecName1, ExcelMessageEnum.PRODUCT_VARIANT_NOT_SUPPORT_CUSTOM));
                            }
                        }
                    }

                    // 第一规格
                    Long sourceId1 = productGlobalAttribute1.getId();
                    ProductAttribute productAttribute1 = CollUtil.get(optionalSpecList, 0);
                    String attributeName1 = productAttribute1.getAttributeName();
                    JSONArray attributeValues1 = productAttribute1.getAttributeValues();
                    if (!CollUtil.contains(attributeValues1, optionalSpecValue1)) {
                        // throw new ExcelMessageException(builder.build(ProductImportDTO::getOptionalSpecValue1, ExcelMessageEnum.PRODUCT_VARIANT_VALUE_REPEAT));
                        productAttribute1.addAttributeValue(optionalSpecValue1);
                    }
                    specComposeNameList.add(attributeName1 + "-" + optionalSpecValue1);

                    ProductSkuAttribute skuAttribute1 = new ProductSkuAttribute();
                    skuAttribute1.setAttributeType(AttributeTypeEnum.OptionalSpec);
                    skuAttribute1.setAttributeSort(0);
                    skuAttribute1.setAttributeName(attributeName1);
                    skuAttribute1.setAttributeValue(optionalSpecValue1);
                    skuAttribute1.setAttributeSourceId(sourceId1);
                    skuAttributeList.add(skuAttribute1);

                    // 第二规格
                    ProductGlobalAttribute productGlobalAttribute2 = CollUtil.get(globalAttributeList, 1);
                    if (productGlobalAttribute2 != null) {
                        if (StrUtil.isBlank(optionalSpecValue2)) {
                            throw new ExcelMessageException(builder.build(ProductImportDTO::getOptionalSpecValue2, ExcelMessageEnum.REQUIRE));
                        }

                        Long sourceId2 = productGlobalAttribute2.getId();
                        Boolean isSupportCustom = productGlobalAttribute2.getIsSupportCustom();
                        if (!isSupportCustom) {
                            JSONArray attributeValues = productGlobalAttribute2.getAttributeValues();
                            // 且当前填写的属性值又不存在规定的值数组内，报错提示
                            if (!attributeValues.contains(optionalSpecValue2)) {
                                throw new ExcelMessageException(builder.build(ProductImportDTO::getOptionalSpecName2, ExcelMessageEnum.PRODUCT_VARIANT_NOT_SUPPORT_CUSTOM));
                            }
                        }

                        ProductAttribute productAttribute2 = CollUtil.get(optionalSpecList, 1);
                        String attributeName2 = productAttribute2.getAttributeName();
                        JSONArray attributeValues2 = productAttribute2.getAttributeValues();
                        if (!CollUtil.contains(attributeValues2, optionalSpecValue2)) {
                            // throw new ExcelMessageException(builder.build(ProductImportDTO::getOptionalSpecValue2, ExcelMessageEnum.PRODUCT_VARIANT_VALUE_REPEAT));
                            productAttribute2.addAttributeValue(optionalSpecValue2);
                        }
                        specComposeNameList.add(attributeName2 + "-" + optionalSpecValue2);

                        ProductSkuAttribute skuAttribute2 = new ProductSkuAttribute();
                        skuAttribute2.setAttributeType(AttributeTypeEnum.OptionalSpec);
                        skuAttribute2.setAttributeSort(1);
                        skuAttribute2.setAttributeName(attributeName2);
                        skuAttribute2.setAttributeValue(optionalSpecValue2);
                        skuAttribute2.setAttributeSourceId(sourceId2);
                        skuAttributeList.add(skuAttribute2);
                    }

                    String warehouseSystemCode = dto.getWarehouseSystemCode();
                    String logisticsTemplateName = dto.getLogisticsTemplateName();
                    Integer quantity = dto.getQuantity();
                    BigDecimal packLength = dto.getPackLength();
                    BigDecimal packWidth = dto.getPackWidth();
                    BigDecimal packHeight = dto.getPackHeight();
                    BigDecimal packWeight = dto.getPackWeight();
                    BigDecimal length = dto.getLength();
                    BigDecimal width = dto.getWidth();
                    BigDecimal height = dto.getHeight();
                    BigDecimal weight = dto.getWeight();
                    Boolean samePacking = StrUtil.equals(dto.getSamePacking(), "Yes");

                    BigDecimal originalUnitPrice = dto.getOriginalUnitPrice();
                    BigDecimal originalOperationFee = dto.getOriginalOperationFee();
                    BigDecimal originalFinalDeliveryFee = dto.getOriginalFinalDeliveryFee();
                    BigDecimal msrp = dto.getMsrp();

                    if (NumberUtil.isLess(originalUnitPrice, BigDecimal.ZERO)) {
                        throw new ExcelMessageException(builder.build(ProductImportDTO::getOriginalUnitPrice, ExcelMessageEnum.PRICE_CANNOT_BE_LESS_THAN_ZERO));
                    }
                    if (NumberUtil.isLess(originalOperationFee, BigDecimal.ZERO)) {
                        throw new ExcelMessageException(builder.build(ProductImportDTO::getOriginalOperationFee, ExcelMessageEnum.PRICE_CANNOT_BE_LESS_THAN_ZERO));
                    }
                    if (NumberUtil.isLess(originalFinalDeliveryFee, BigDecimal.ZERO)) {
                        throw new ExcelMessageException(builder.build(ProductImportDTO::getOriginalFinalDeliveryFee, ExcelMessageEnum.PRICE_CANNOT_BE_LESS_THAN_ZERO));
                    }
                    if (msrp != null && NumberUtil.isLess(msrp, BigDecimal.ZERO)) {
                        throw new ExcelMessageException(builder.build(ProductImportDTO::getMsrp, ExcelMessageEnum.PRICE_CANNOT_BE_LESS_THAN_ZERO));
                    }

                    List<String> imageList = new ArrayList<>();
                    imageList.add(dto.getImage1());
                    imageList.add(dto.getImage2());
                    imageList.add(dto.getImage3());
                    imageList.add(dto.getImage4());
                    imageList.add(dto.getImage5());
                    imageList.add(dto.getImage6());
                    imageList.addAll(StrUtil.split(dto.getOtherImage(), ";"));
                    CollUtil.removeBlank(imageList);

                    String productSkuCode = productCodeGenerator.codeGenerate(BusinessCodeEnum.ProductSkuCode);
                    ProductSkuImportBo productSku = new ProductSkuImportBo();
                    productSku.setProductCode(productCode);
                    productSku.setProductSkuCode(productSkuCode);
                    productSku.setName(productName);
                    productSku.setSku(sku);
                    productSku.setErpSku(erpSku);
                    productSku.setUpc(upc);
                    productSku.setShelfState(ShelfStateEnum.ForcedOffShelf);
                    productSku.setVerifyState(ProductVerifyStateEnum.Draft);
                    productSku.setSpecComposeName(StrUtil.addSuffixIfNot(CollUtil.join(specComposeNameList, ";"), ";"));
                    productSku.setSpecValName(specValName);
                    productSku.setSort(CollUtil.size(productSkuList));
                    productSku.setStockTotal(0);

                    ProductSkuDetail productSkuDetail = new ProductSkuDetail();
                    productSkuDetail.setLength(length);
                    productSkuDetail.setWidth(width);
                    productSkuDetail.setHeight(height);
                    productSkuDetail.setLengthUnit(LengthUnitEnum.inch);
                    productSkuDetail.setWeight(weight);
                    productSkuDetail.setWeightUnit(WeightUnitEnum.lb);

                    if (samePacking) {
                        packLength = length;
                        packWidth = width;
                        packHeight = height;
                        packWeight = weight;
                    } else {
                        if (packLength == null) {
                            throw new ExcelMessageException(builder.build(ProductImportDTO::getPackLength, ExcelMessageEnum.REQUIRE));
                        }
                        if (packWidth == null) {
                            throw new ExcelMessageException(builder.build(ProductImportDTO::getPackWidth, ExcelMessageEnum.REQUIRE));
                        }
                        if (packHeight == null) {
                            throw new ExcelMessageException(builder.build(ProductImportDTO::getPackHeight, ExcelMessageEnum.REQUIRE));
                        }
                        if (packWeight == null) {
                            throw new ExcelMessageException(builder.build(ProductImportDTO::getPackWeight, ExcelMessageEnum.REQUIRE));
                        }
                    }

                    productSkuDetail.setPackLength(packLength);
                    productSkuDetail.setPackWidth(packWidth);
                    productSkuDetail.setPackHeight(packHeight);
                    productSkuDetail.setPackLengthUnit(LengthUnitEnum.inch);
                    productSkuDetail.setPackWeight(packWeight);
                    productSkuDetail.setPackWeightUnit(WeightUnitEnum.lb);
                    productSkuDetail.setSamePacking(samePacking);
                    productSkuDetail.setDescription(description);

                    // 处理图片
                    List<ProductSkuAttachment> skuAttachmentList = downloadImage(imageList);
                    if (CollUtil.isEmpty(skuAttachmentList)) {
                        throw new ExcelMessageException(builder.build(ProductImportDTO::getWarehouseSystemCode, ExcelMessageEnum.INVALID_IMAGE));
                    }

                    // 处理仓库与库存
                    List<ProductSkuStock> skuStockList = new ArrayList<>();
                    StockManagerEnum stockManagerEnum = null;
                    List<String> warehouseSystemCodeList = StrUtil.split(warehouseSystemCode, ";");
                    for (String code : warehouseSystemCodeList) {
                        ProductSkuStock productSkuStock = new ProductSkuStock();
                        String stockCode = productCodeGenerator.codeGenerate(BusinessCodeEnum.StockCode);

                        Warehouse warehouse = iWarehouseService.queryByWarehouseSystemCodeHasTenant(tenantId, code);
                        if (warehouse == null) {
                            throw new ExcelMessageException(builder.build(ProductImportDTO::getWarehouseSystemCode, ExcelMessageEnum.WAREHOUSE_NOT_FOUND));
                        }

                        WarehouseTypeEnum warehouseType = warehouse.getWarehouseType();
                        StockManagerEnum nowStockManager = warehouseType.toStockManager();
                        if (stockManagerEnum == null) {
                            stockManagerEnum = nowStockManager;
                        } else if (ObjectUtil.notEqual(stockManagerEnum, nowStockManager)) {
                            throw new ExcelMessageException(builder.build(ProductImportDTO::getWarehouseSystemCode, ExcelMessageEnum.WAREHOUSE_TYPE_MUST_BE_SAME));
                        }

                        String logisticsTemplateNo = null;
                        if (StrUtil.isNotBlank(logisticsTemplateName)) {
                            LogisticsTemplate logisticsTemplate = iLogisticsTemplateService.queryByWarehouse(code, logisticsTemplateName);
                            if (logisticsTemplate == null) {
                                throw new ExcelMessageException(builder.build(ProductImportDTO::getLogisticsTemplateName, ExcelMessageEnum.LOGISTICS_TEMPLATE_NOT_FOUND));
                            }
                            logisticsTemplateNo = logisticsTemplate.getLogisticsTemplateNo();
                        }

                        productSkuStock.setStockCode(stockCode);
                        productSkuStock.setStockTotal(quantity);
                        productSkuStock.setStockReserved(0);
                        productSkuStock.setStockAvailable(quantity);
                        productSkuStock.setStockState(GlobalStateEnum.Valid);
                        productSkuStock.setErpSku(erpSku);
                        productSkuStock.setProductCode(productCode);
                        productSkuStock.setProductSkuCode(productSkuCode);
                        productSkuStock.setWarehouseSystemCode(warehouseSystemCode);
                        productSkuStock.setLogisticsTemplateNo(logisticsTemplateNo);
                        skuStockList.add(productSkuStock);
                    }

                    productSku.setStockManager(stockManagerEnum);

                    // 价格数据
                    ProductSkuPrice productSkuPrice = new ProductSkuPrice();
                    productSkuPrice.setOriginalUnitPrice(originalUnitPrice);
                    productSkuPrice.setOriginalOperationFee(originalOperationFee);
                    productSkuPrice.setOriginalFinalDeliveryFee(originalFinalDeliveryFee);
                    productSkuPrice.setOriginalPickUpPrice(NumberUtil.add(originalUnitPrice, originalOperationFee));
                    productSkuPrice.setOriginalDropShippingPrice(NumberUtil.add(originalUnitPrice, originalOperationFee, originalFinalDeliveryFee));

                    productSkuPrice = iProductSkuPriceRuleService.matchRule(productSkuCode, originalUnitPrice, originalOperationFee, originalFinalDeliveryFee, false);
                    productSkuPrice.setProductSkuCode(productSkuCode);
                    productSkuPrice.setMsrp(msrp);

                    productSku.setSkuDetail(productSkuDetail);
                    productSku.setSkuPrice(productSkuPrice);
                    productSku.setSkuStockList(skuStockList);
                    productSku.setSkuAttributeList(skuAttributeList);
                    productSku.setSkuAttachmentList(skuAttachmentList);
                    product.addProductSku(productSku);
                    product.addSpecValName(specValName);
                    product.setProductAttributeList(productAttributeList);
                    product.setOptionalSpecList(optionalSpecList);
                    product.setGlobalAttributeList(globalAttributeList);
                    productGroupMap.put(group, product);
                } catch (ExcelMessageException e) {
                    globalMessage.append(e.getLocaleMessage());
                } catch (Exception e) {
                    globalMessage.append(builder.buildOnlyRow(ExcelMessageEnum.UNKNOWN_PARSING_ERROR));
                    log.error("商品上传，第{}行出现未知的解析错误，原因 {}", showRowIndex, e.getMessage(), e);
                }
            }

            if (globalMessage.hasData()) {
                record.setImportMessage(globalMessage.toJSON());
                record.setImportState(ImportStateEnum.Failed);
                iProductImportRecordService.updateById(record);
            } else {
                Collection<ProductImportBo> productImportBoList = productGroupMap.values();
                try {
                    productSupport.saveProductImport(tenantId, productImportBoList);
                    record.setImportState(ImportStateEnum.Success);
                    record.setImportProducts(CollUtil.size(productImportBoList));
                } catch (DataIntegrityViolationException e) {
                    log.error("保存导入商品出现数据库异常，原因 {}", e.getMessage(), e);
                    LocaleMessage localeMessage = LocaleMessage.byStatusCode(ZSMallStatusCodeEnum.SAVE_IMPORT_DATA_ERROR);
                    Throwable cause = e.getCause();
                    if (cause != null) {
                        String message = cause.getMessage();
                        if (StrUtil.contains(message, "Data too long for column")) {
                            localeMessage = LocaleMessage.byStatusCode(ZSMallStatusCodeEnum.DATABASE_DATA_TOO_LONG);
                        }
                    }
                    record.setImportMessage(localeMessage.toJSON());
                    record.setImportState(ImportStateEnum.Failed);
                } catch (Exception e) {
                    log.error("保存导入商品出现未知异常，原因 {}", e.getMessage(), e);
                    record.setImportMessage(LocaleMessage.byStatusCodeToJSON(ZSMallStatusCodeEnum.SAVE_IMPORT_DATA_ERROR));
                    record.setImportState(ImportStateEnum.Failed);
                }
                iProductImportRecordService.updateById(record);
            }
        });
        return R.ok();
    }

    @Override
    public R<Void> uploadProductExcelNotAsync(MultipartFile file) throws Exception {
        String tenantId = LoginHelper.getTenantId();
        List<SiteCountryCurrency> countryCurrencyList = iSiteCountryCurrencyService.list();
        Map<String, SiteCountryCurrency> siteCountryCurrencyMap = countryCurrencyList.stream()
                                                                    .collect(Collectors.toMap(SiteCountryCurrency::getCountryCode, Function.identity()));
        if (file == null) {
            return R.fail(ZSMallStatusCodeEnum.UPLOAD_FILE_IS_EMPTY);
        }

        Boolean existsed = iProductImportRecordService.existImportingRecord();
        if (existsed) {
            return R.fail(ZSMallStatusCodeEnum.PRODUCT_IMPORT_FILE_EXIST_IMPORTING);
        }

        InputStream inputStream = file.getInputStream();
        ExcelReader reader = ExcelUtil.getReader(inputStream);

        if (reader != null) {
            Sheet sheet = reader.getSheet();
            String sheetName = sheet.getSheetName();
            log.info("上传商品Excel - sheetName = {}", sheetName);
            int columnCount = reader.getColumnCount();
            log.info("上传商品Excel - columnCount = {}", columnCount);
            if (columnCount != 56) {
                return R.fail(ZSMallStatusCodeEnum.EXCEL_COLUMN_COUNT_NOT_MATCH);
            }
        }

        // 初始化信息构建者
        ExcelMsgBuilder<ProductImportDTO> builder = ZExcelUtil.msgBuilder(reader, 1, ProductImportDTO.class);
        builder.setMsgPrefix("<p>").setMsgSuffix("</p></br>");

        ProductImportRecord record = new ProductImportRecord();
        record.setImportRecordNo(productCodeGenerator.codeGenerate(BusinessCodeEnum.ProductImportRecordNo));
        record.setImportFileName(file.getOriginalFilename());
        record.setImportState(ImportStateEnum.Importing);
        iProductImportRecordService.save(record);

        List<ProductImportDTO> dtoList;
        try {
            dtoList = ZExcelUtil.parseFieldDTO(reader, ProductImportDTO.class, 1, 10);
            if (CollUtil.isEmpty(dtoList)) {
                throw new ExcelMessageException(builder.build(ExcelMessageEnum.NOT_VALID_ROW));
            }
        } catch (ExcelMessageException e) {
            record.setImportMessage(e.getLocaleMessage().toJSON());
            record.setImportState(ImportStateEnum.Failed);
            iProductImportRecordService.updateById(record);
            return null;
        }

        LocaleMessage globalMessage = new LocaleMessage();
        // 全局Sku查重
        List<String> globalSku = new ArrayList<>();
        // 全局Upc查重
        List<String> globalUpc = new ArrayList<>();

        Map<String, ProductImportBo> productGroupMap = new HashMap<>();
        // 导入记录完成
        // 保存产品相关信息
        for (ProductImportDTO dto : dtoList) {
            // 每次循环开始设置当前的行号，以保证构建信息时展示的行号是正确的
            int showRowIndex = dto.getShowRowIndex();
            builder.setNowShowRow(showRowIndex);

            try {
                String group = dto.getGroup();
                ProductImportBo product = productGroupMap.get(group);
                List<ProductAttribute> productAttributeList;

                List<ProductSkuImportBo> productSkuList;
                List<ProductGlobalAttribute> globalAttributeList;
                List<ProductAttribute> optionalSpecList;
                if (product == null) {
                    product = new ProductImportBo();

                    String category = dto.getCategory();
                    String productName = dto.getProductName();
                    String supportedLogistics = dto.getSupportedLogistics();
                    String forbiddenChannel = dto.getForbiddenChannel();
                    String description = dto.getDescription();

                    if (StrUtil.isBlank(category)) {
                        throw new ExcelMessageException(builder.build(ProductImportDTO::getCategory, ExcelMessageEnum.REQUIRE));
                    }
                    if (StrUtil.isBlank(productName)) {
                        throw new ExcelMessageException(builder.build(ProductImportDTO::getProductName, ExcelMessageEnum.REQUIRE));
                    }
                    if (StrUtil.isBlank(supportedLogistics)) {
                        throw new ExcelMessageException(builder.build(ProductImportDTO::getSupportedLogistics, ExcelMessageEnum.REQUIRE));
                    }

                    if (productName.length() > 150) {
                        throw new ExcelMessageException(builder.build(ProductImportDTO::getProductName, ExcelMessageEnum.PRODUCT_NAME_LIMIT));
                    }

                    SupportedLogisticsEnum supportedLogisticsEnum;
                    // 支持的物流类型
                    if (StrUtil.equals(supportedLogistics, "Pick Up & Dropshipping")) {
                        supportedLogisticsEnum = SupportedLogisticsEnum.All;
                    } else if (StrUtil.equals(supportedLogistics, "Pick Up Only")) {
                        supportedLogisticsEnum = SupportedLogisticsEnum.PickUpOnly;
                    } else if (StrUtil.equals(supportedLogistics, "Dropshipping Only")) {
                        supportedLogisticsEnum = SupportedLogisticsEnum.DropShippingOnly;
                    } else {
                        throw new ExcelMessageException(builder.build(ProductImportDTO::getSupportedLogistics, ExcelMessageEnum.ILLEGAL_ARGUMENT));
                    }

                    // 禁售渠道
                    JSONArray forbiddenChannelArray = new JSONArray();
                    if (StrUtil.isNotBlank(forbiddenChannel)) {
                        forbiddenChannel = StrUtil.replace(forbiddenChannel, "；", ";");
                        List<String> channelList = StrUtil.split(forbiddenChannel, ";");
                        for (String channel : channelList) {
                            try {
                                ChannelTypeEnum channelType = ChannelTypeEnum.valueOf(channel);
                                forbiddenChannelArray.add(channelType.name());
                            } catch (Exception e) {
                            }
                        }
                    }

//                        if (!forbiddenChannelArray.contains(ChannelTypeEnum.Wayfair.name())) {
//                            forbiddenChannelArray.add(ChannelTypeEnum.Wayfair.name());
//                        }

                    // 分类处理
                    String finalCateoryName;
                    if (StrUtil.contains(category, "/")) {
                        List<String> categoryList = StrUtil.split(category, "/");
                        finalCateoryName = CollUtil.getLast(categoryList);
                    } else {
                        finalCateoryName = category;
                    }

                    List<ProductCategory> productCategoryList = iProductCategoryService.queryByCategoryName(category);
                    if (CollUtil.isEmpty(productCategoryList)) {
                        throw new ExcelMessageException(builder.build(ProductImportDTO::getCategory, ExcelMessageEnum.PRODUCT_CATEGORY_NOT_FOUND));
                    }
                    ProductCategory productCategory = productCategoryList.get(0);
                    Long belongCategoryId = productCategory.getId();

                    // 处理分类关系
                    List<ProductCategory> productCategoryTree = iProductCategoryService.queryCategoryChainById(belongCategoryId);
                    List<ProductCategoryRelation> categoryRelationList = new ArrayList<>();
                    if (CollUtil.isNotEmpty(productCategoryList)) {
                        for (ProductCategory one_productCategoryTree : productCategoryTree) {
                            ProductCategoryRelation pcr = new ProductCategoryRelation();
                            pcr.setProductCategoryId(one_productCategoryTree.getId());
                            categoryRelationList.add(pcr);
                        }
                    }

                    String productCode = productCodeGenerator.codeGenerate(BusinessCodeEnum.ProductCode);
                    product.setName(productName);
                    product.setBelongCategoryId(belongCategoryId);
                    product.setSupportedLogistics(supportedLogisticsEnum);
                    product.setDescription(StrUtil.emptyToNull(description));
                    product.setProductCode(productCode);
                    product.setProductType(ProductTypeEnum.NormalProduct);
                    product.setForbiddenChannel(forbiddenChannelArray);
                    product.setShelfState(ShelfStateEnum.ForcedOffShelf);
                    product.setVerifyState(ProductVerifyStateEnum.Draft);
                    product.setDownloadCount(0);
                    product.setCategoryRelationList(categoryRelationList);

                    productAttributeList = new ArrayList<>();
                    // 处理商品特色 和 通用规格
                    // 查询是否员工设置了该分类的必填的通用规格，有的话也要加入到待保存数组中
                    List<ProductGlobalAttributeSimpleVo> requiredGlobalAttributeList = iProductGlobalAttributeService.queryByProductCategoryIdAndRequired(product.getBelongCategoryId(), AttributeBelongEnum.Platform.getValue(),
                        AttributeScopeEnum.GenericSpec.getValue(), true);
                    if (CollUtil.isNotEmpty(requiredGlobalAttributeList)) {
                        for (int i = 0; i < requiredGlobalAttributeList.size(); i++) {
                            ProductGlobalAttributeSimpleVo attributeSimpleVo = requiredGlobalAttributeList.get(i);
                            ProductAttribute productAttribute = productSupport.setProductAttribute(null, null, attributeSimpleVo.getAttributeName(), null, AttributeTypeEnum.GenericSpec, attributeSimpleVo.getId(), i);
                            productAttributeList.add(productAttribute);
                        }
                    }

                    int genericSpecSort = CollUtil.size(productAttributeList);
                    for (int i = 1; i <= 5; i++) {
                        Object featureName = ReflectUtil.getFieldValue(dto, "featureName" + i);
                        Object featureValue = ReflectUtil.getFieldValue(dto, "featureValue" + i);
                        if (ObjectUtil.isAllNotEmpty(featureName, featureValue)) {
                            ProductAttribute productAttribute = productSupport.setProductAttribute(null, null, featureName.toString(), featureValue.toString(), AttributeTypeEnum.Feature, null, i - 1);
                            productAttributeList.add(productAttribute);
                        }

                        Object genericSpecName = ReflectUtil.getFieldValue(dto, "genericSpecName" + i);
                        Object genericSpecValue = ReflectUtil.getFieldValue(dto, "genericSpecValue" + i);
                        if (ObjectUtil.isAllNotEmpty(genericSpecName, genericSpecValue)) {
                            ProductAttribute productAttribute = productSupport.setProductAttribute(null, null, genericSpecName.toString(), genericSpecValue.toString(), AttributeTypeEnum.GenericSpec, null, genericSpecSort + i);
                            productAttributeList.add(productAttribute);
                        }
                    }

                    productSkuList = new ArrayList<>();
                    globalAttributeList = new ArrayList<>();
                    optionalSpecList = new ArrayList<>();

                    // 处理可选规格
                    String optionalSpecName1 = StrUtil.trim(dto.getOptionalSpecName1());
                    if (StrUtil.isBlank(optionalSpecName1)) {
                        throw new ExcelMessageException(builder.build(ProductImportDTO::getOptionalSpecName1, ExcelMessageEnum.REQUIRE));
                    }

                    String optionalSpecName2 = StrUtil.trim(dto.getOptionalSpecName2());

                    ProductGlobalAttribute productGlobalAttribute1 = iProductGlobalAttributeService.queryByAttributeNameAndScopeAndCategory(tenantId, optionalSpecName1,
                        AttributeScopeEnum.OptionalSpec, product.getBelongCategoryId());
                    if (productGlobalAttribute1 == null) {
                        throw new ExcelMessageException(builder.build(ProductImportDTO::getOptionalSpecName1, ExcelMessageEnum.PRODUCT_VARIANT_DIMENSION_NOT_FOUND));
                    }
                    ProductAttribute productAttribute1 = productSupport.setProductAttribute(null, null, optionalSpecName1, null, AttributeTypeEnum.OptionalSpec, productGlobalAttribute1.getId(), 0);
                    globalAttributeList.add(productGlobalAttribute1);
                    optionalSpecList.add(productAttribute1);

                    if (StrUtil.isNotBlank(optionalSpecName2)) {
                        // 重复的变体维度
                        if (StrUtil.equals(optionalSpecName1, optionalSpecName2)) {
                            throw new ExcelMessageException(builder.build(ProductImportDTO::getOptionalSpecName2, ExcelMessageEnum.DUPLICATE_VARIANT));
                        }

                        ProductGlobalAttribute productGlobalAttribute2 = iProductGlobalAttributeService.queryByAttributeNameAndScopeAndCategory(tenantId, optionalSpecName2,
                            AttributeScopeEnum.OptionalSpec, product.getBelongCategoryId());
                        if (productGlobalAttribute2 == null) {
                            throw new ExcelMessageException(builder.build(ProductImportDTO::getOptionalSpecName2, ExcelMessageEnum.PRODUCT_VARIANT_DIMENSION_NOT_FOUND));
                        }
                        ProductAttribute productAttribute2 = productSupport.setProductAttribute(null, null, optionalSpecName2, null, AttributeTypeEnum.OptionalSpec, productGlobalAttribute2.getId(), 0);
                        globalAttributeList.add(productGlobalAttribute2);
                        optionalSpecList.add(productAttribute2);
                    }
                } else {
                    productAttributeList = product.getProductAttributeList();
                    optionalSpecList = product.getOptionalSpecList();
                    globalAttributeList = product.getGlobalAttributeList();
                    productSkuList = product.getProductSkuVoList();
                }
                String productName = product.getName();
                String productCode = product.getProductCode();
                String description = product.getDescription();
                Long belongCategoryId = product.getBelongCategoryId();
                product.setProductAttributeList(productAttributeList);

                String sku = dto.getSku();
                String erpSku = dto.getErpSku();
                String upc = dto.getUpc();
                String optionalSpecValue1 = StrUtil.trim(dto.getOptionalSpecValue1());
                String optionalSpecValue2 = StrUtil.trim(dto.getOptionalSpecValue2());

                boolean existSku = iProductSkuService.existSku(sku, tenantId, null);
                if (existSku || globalSku.contains(sku)) {
                    throw new ExcelMessageException(builder.build(ProductImportDTO::getSku, ExcelMessageEnum.SKU_REPEAT));
                } else {
                    globalSku.add(sku);
                }

                if (StrUtil.isNotBlank(upc)) {
                    boolean existUpc = iProductSkuService.existUpc(upc, null);
                    if (existUpc || globalUpc.contains(upc)) {
                        throw new ExcelMessageException(builder.build(ProductImportDTO::getUpc, ExcelMessageEnum.UPC_REPEAT));
                    } else {
                        globalUpc.add(upc);
                    }
                }

                List<String> specComposeNameList = new ArrayList<>();
                List<String> specValNameList = new ArrayList<>();
                specValNameList.add(optionalSpecValue1);
                specValNameList.add(optionalSpecValue2);
                String specValName = CollUtil.join(CollUtil.removeEmpty(specValNameList), "/");
                Boolean containsSpecValName = product.containsSpecValName(specValName);
                if (containsSpecValName) {
                    throw new ExcelMessageException(builder.buildOnlyRow(ExcelMessageEnum.DUPLICATE_VARIANT_VALUE_COMBINATION));
                }

                List<ProductSkuAttribute> skuAttributeList = new ArrayList<>();
                ProductGlobalAttribute productGlobalAttribute1 = CollUtil.get(globalAttributeList, 0);
                if (productGlobalAttribute1 != null) {
                    Boolean isSupportCustom = productGlobalAttribute1.getIsSupportCustom();
                    // 不支持自定义属性值
                    if (!isSupportCustom) {
                        JSONArray attributeValues = productGlobalAttribute1.getAttributeValues();
                        // 且当前填写的属性值又不存在规定的值数组内，报错提示
                        if (!attributeValues.contains(optionalSpecValue1)) {
                            throw new ExcelMessageException(builder.build(ProductImportDTO::getOptionalSpecName1, ExcelMessageEnum.PRODUCT_VARIANT_NOT_SUPPORT_CUSTOM));
                        }
                    }
                }

                // 第一规格
                Long sourceId1 = productGlobalAttribute1.getId();
                ProductAttribute productAttribute1 = CollUtil.get(optionalSpecList, 0);
                String attributeName1 = productAttribute1.getAttributeName();
                JSONArray attributeValues1 = productAttribute1.getAttributeValues();
                if (!CollUtil.contains(attributeValues1, optionalSpecValue1)) {
                    // throw new ExcelMessageException(builder.build(ProductImportDTO::getOptionalSpecValue1, ExcelMessageEnum.PRODUCT_VARIANT_VALUE_REPEAT));
                    productAttribute1.addAttributeValue(optionalSpecValue1);
                }
                specComposeNameList.add(attributeName1 + "-" + optionalSpecValue1);

                ProductSkuAttribute skuAttribute1 = new ProductSkuAttribute();
                skuAttribute1.setAttributeType(AttributeTypeEnum.OptionalSpec);
                skuAttribute1.setAttributeSort(0);
                skuAttribute1.setAttributeName(attributeName1);
                skuAttribute1.setAttributeValue(optionalSpecValue1);
                skuAttribute1.setAttributeSourceId(sourceId1);
                skuAttributeList.add(skuAttribute1);

                // 第二规格
                ProductGlobalAttribute productGlobalAttribute2 = CollUtil.get(globalAttributeList, 1);
                if (productGlobalAttribute2 != null) {
                    if (StrUtil.isBlank(optionalSpecValue2)) {
                        throw new ExcelMessageException(builder.build(ProductImportDTO::getOptionalSpecValue2, ExcelMessageEnum.REQUIRE));
                    }

                    Long sourceId2 = productGlobalAttribute2.getId();
                    Boolean isSupportCustom = productGlobalAttribute2.getIsSupportCustom();
                    if (!isSupportCustom) {
                        JSONArray attributeValues = productGlobalAttribute2.getAttributeValues();
                        // 且当前填写的属性值又不存在规定的值数组内，报错提示
                        if (!attributeValues.contains(optionalSpecValue2)) {
                            throw new ExcelMessageException(builder.build(ProductImportDTO::getOptionalSpecName2, ExcelMessageEnum.PRODUCT_VARIANT_NOT_SUPPORT_CUSTOM));
                        }
                    }

                    ProductAttribute productAttribute2 = CollUtil.get(optionalSpecList, 1);
                    String attributeName2 = productAttribute2.getAttributeName();
                    JSONArray attributeValues2 = productAttribute2.getAttributeValues();
                    if (!CollUtil.contains(attributeValues2, optionalSpecValue2)) {
                        // throw new ExcelMessageException(builder.build(ProductImportDTO::getOptionalSpecValue2, ExcelMessageEnum.PRODUCT_VARIANT_VALUE_REPEAT));
                        productAttribute2.addAttributeValue(optionalSpecValue2);
                    }
                    specComposeNameList.add(attributeName2 + "-" + optionalSpecValue2);

                    ProductSkuAttribute skuAttribute2 = new ProductSkuAttribute();
                    skuAttribute2.setAttributeType(AttributeTypeEnum.OptionalSpec);
                    skuAttribute2.setAttributeSort(1);
                    skuAttribute2.setAttributeName(attributeName2);
                    skuAttribute2.setAttributeValue(optionalSpecValue2);
                    skuAttribute2.setAttributeSourceId(sourceId2);
                    skuAttributeList.add(skuAttribute2);
                }

                String warehouseSystemCode = dto.getWarehouseSystemCode();
                String logisticsTemplateName = dto.getLogisticsTemplateName();
                Integer quantity = dto.getQuantity();
                BigDecimal packLength = dto.getPackLength();
                BigDecimal packWidth = dto.getPackWidth();
                BigDecimal packHeight = dto.getPackHeight();
                BigDecimal packWeight = dto.getPackWeight();
                BigDecimal length = dto.getLength();
                BigDecimal width = dto.getWidth();
                BigDecimal height = dto.getHeight();
                BigDecimal weight = dto.getWeight();
                Boolean samePacking = StrUtil.equals(dto.getSamePacking(), "Yes");

                BigDecimal originalUnitPrice = dto.getOriginalUnitPrice();
                BigDecimal originalOperationFee = dto.getOriginalOperationFee();
                BigDecimal originalFinalDeliveryFee = dto.getOriginalFinalDeliveryFee();
                BigDecimal msrp = dto.getMsrp();

                if (NumberUtil.isLess(originalUnitPrice, BigDecimal.ZERO)) {
                    throw new ExcelMessageException(builder.build(ProductImportDTO::getOriginalUnitPrice, ExcelMessageEnum.PRICE_CANNOT_BE_LESS_THAN_ZERO));
                }
                if (NumberUtil.isLess(originalOperationFee, BigDecimal.ZERO)) {
                    throw new ExcelMessageException(builder.build(ProductImportDTO::getOriginalOperationFee, ExcelMessageEnum.PRICE_CANNOT_BE_LESS_THAN_ZERO));
                }
                if (NumberUtil.isLess(originalFinalDeliveryFee, BigDecimal.ZERO)) {
                    throw new ExcelMessageException(builder.build(ProductImportDTO::getOriginalFinalDeliveryFee, ExcelMessageEnum.PRICE_CANNOT_BE_LESS_THAN_ZERO));
                }
                if (msrp != null && NumberUtil.isLess(msrp, BigDecimal.ZERO)) {
                    throw new ExcelMessageException(builder.build(ProductImportDTO::getMsrp, ExcelMessageEnum.PRICE_CANNOT_BE_LESS_THAN_ZERO));
                }

                List<String> imageList = new ArrayList<>();
                imageList.add(dto.getImage1());
                imageList.add(dto.getImage2());
                imageList.add(dto.getImage3());
                imageList.add(dto.getImage4());
                imageList.add(dto.getImage5());
                imageList.add(dto.getImage6());
                imageList.addAll(StrUtil.split(dto.getOtherImage(), ";"));
                CollUtil.removeBlank(imageList);

                String productSkuCode = productCodeGenerator.codeGenerate(BusinessCodeEnum.ProductSkuCode);
                ProductSkuImportBo productSku = new ProductSkuImportBo();
                productSku.setProductCode(productCode);
                productSku.setProductSkuCode(productSkuCode);
                productSku.setName(productName);
                productSku.setSku(sku);
                productSku.setErpSku(erpSku);
                productSku.setUpc(upc);
                productSku.setShelfState(ShelfStateEnum.ForcedOffShelf);
                productSku.setVerifyState(ProductVerifyStateEnum.Draft);
                productSku.setSpecComposeName(StrUtil.addSuffixIfNot(CollUtil.join(specComposeNameList, ";"), ";"));
                productSku.setSpecValName(specValName);
                productSku.setSort(CollUtil.size(productSkuList));
                productSku.setStockTotal(0);

                ProductSkuDetail productSkuDetail = new ProductSkuDetail();
                productSkuDetail.setLength(length);
                productSkuDetail.setWidth(width);
                productSkuDetail.setHeight(height);
                productSkuDetail.setLengthUnit(LengthUnitEnum.inch);
                productSkuDetail.setWeight(weight);
                productSkuDetail.setWeightUnit(WeightUnitEnum.lb);

                if (samePacking) {
                    packLength = length;
                    packWidth = width;
                    packHeight = height;
                    packWeight = weight;
                } else {
                    if (packLength == null) {
                        throw new ExcelMessageException(builder.build(ProductImportDTO::getPackLength, ExcelMessageEnum.REQUIRE));
                    }
                    if (packWidth == null) {
                        throw new ExcelMessageException(builder.build(ProductImportDTO::getPackWidth, ExcelMessageEnum.REQUIRE));
                    }
                    if (packHeight == null) {
                        throw new ExcelMessageException(builder.build(ProductImportDTO::getPackHeight, ExcelMessageEnum.REQUIRE));
                    }
                    if (packWeight == null) {
                        throw new ExcelMessageException(builder.build(ProductImportDTO::getPackWeight, ExcelMessageEnum.REQUIRE));
                    }
                }

                productSkuDetail.setPackLength(packLength);
                productSkuDetail.setPackWidth(packWidth);
                productSkuDetail.setPackHeight(packHeight);
                productSkuDetail.setPackLengthUnit(LengthUnitEnum.inch);
                productSkuDetail.setPackWeight(packWeight);
                productSkuDetail.setPackWeightUnit(WeightUnitEnum.lb);
                productSkuDetail.setSamePacking(samePacking);
                productSkuDetail.setDescription(description);

                // 处理图片
                List<ProductSkuAttachment> skuAttachmentList = downloadImage(imageList);
                if (CollUtil.isEmpty(skuAttachmentList)) {
                    throw new ExcelMessageException(builder.build(ProductImportDTO::getWarehouseSystemCode, ExcelMessageEnum.INVALID_IMAGE));
                }

                // 处理仓库与库存
                List<ProductSkuStock> skuStockList = new ArrayList<>();
                StockManagerEnum stockManagerEnum = null;
                List<String> warehouseSystemCodeList = StrUtil.split(warehouseSystemCode, ";");
                for (String code : warehouseSystemCodeList) {
                    ProductSkuStock productSkuStock = new ProductSkuStock();
                    String stockCode = productCodeGenerator.codeGenerate(BusinessCodeEnum.StockCode);

                    Warehouse warehouse = iWarehouseService.queryByWarehouseSystemCodeHasTenant(tenantId, code);
                    if (warehouse == null) {
                        throw new ExcelMessageException(builder.build(ProductImportDTO::getWarehouseSystemCode, ExcelMessageEnum.WAREHOUSE_NOT_FOUND));
                    }

                    WarehouseTypeEnum warehouseType = warehouse.getWarehouseType();
                    StockManagerEnum nowStockManager = warehouseType.toStockManager();
                    if (stockManagerEnum == null) {
                        stockManagerEnum = nowStockManager;
                    } else if (ObjectUtil.notEqual(stockManagerEnum, nowStockManager)) {
                        throw new ExcelMessageException(builder.build(ProductImportDTO::getWarehouseSystemCode, ExcelMessageEnum.WAREHOUSE_TYPE_MUST_BE_SAME));
                    }

                    String logisticsTemplateNo = null;
                    if (StrUtil.isNotBlank(logisticsTemplateName)) {
                        LogisticsTemplate logisticsTemplate = iLogisticsTemplateService.queryByWarehouse(code, logisticsTemplateName);
                        if (logisticsTemplate == null) {
                            throw new ExcelMessageException(builder.build(ProductImportDTO::getLogisticsTemplateName, ExcelMessageEnum.LOGISTICS_TEMPLATE_NOT_FOUND));
                        }
                        logisticsTemplateNo = logisticsTemplate.getLogisticsTemplateNo();
                    }

                    productSkuStock.setStockCode(stockCode);
                    productSkuStock.setStockTotal(quantity);
                    productSkuStock.setStockReserved(0);
                    productSkuStock.setStockAvailable(quantity);
                    productSkuStock.setStockState(GlobalStateEnum.Valid);
                    productSkuStock.setErpSku(erpSku);
                    productSkuStock.setProductCode(productCode);
                    productSkuStock.setProductSkuCode(productSkuCode);
                    productSkuStock.setWarehouseSystemCode(warehouseSystemCode);
                    productSkuStock.setLogisticsTemplateNo(logisticsTemplateNo);
                    skuStockList.add(productSkuStock);
                }

                productSku.setStockManager(stockManagerEnum);

                // 价格数据 todo 此入口价格全部默认美国站点
                ProductSkuPrice productSkuPrice = new ProductSkuPrice();

                SiteCountryCurrency currency = siteCountryCurrencyMap.get("US");


                productSkuPrice.setOriginalUnitPrice(originalUnitPrice);
                productSkuPrice.setOriginalOperationFee(originalOperationFee);
                productSkuPrice.setOriginalFinalDeliveryFee(originalFinalDeliveryFee);
                productSkuPrice.setOriginalPickUpPrice(NumberUtil.add(originalUnitPrice, originalOperationFee));
                productSkuPrice.setOriginalDropShippingPrice(NumberUtil.add(originalUnitPrice, originalOperationFee, originalFinalDeliveryFee));

//                productSkuPrice = iProductSkuPriceRuleService.matchRule(productSkuCode, originalUnitPrice, originalOperationFee, originalFinalDeliveryFee, false);
                productSkuPrice = iProductSkuPriceRuleService.matchRuleAndSite(productSkuCode, originalUnitPrice, originalOperationFee, originalFinalDeliveryFee, false,siteCountryCurrencyMap,"US");

                productSkuPrice.setProductSkuCode(productSkuCode);
                productSkuPrice.setMsrp(msrp);

                productSku.setSkuDetail(productSkuDetail);
                productSku.setSkuPrice(productSkuPrice);
                productSku.setSkuStockList(skuStockList);
                productSku.setSkuAttributeList(skuAttributeList);
                productSku.setSkuAttachmentList(skuAttachmentList);
                product.addProductSku(productSku);
                product.addSpecValName(specValName);
                product.setProductAttributeList(productAttributeList);
                product.setOptionalSpecList(optionalSpecList);
                product.setGlobalAttributeList(globalAttributeList);
                productGroupMap.put(group, product);
            } catch (ExcelMessageException e) {
                globalMessage.append(e.getLocaleMessage());
            } catch (Exception e) {
                globalMessage.append(builder.buildOnlyRow(ExcelMessageEnum.UNKNOWN_PARSING_ERROR));
                log.error("商品上传，第{}行出现未知的解析错误，原因 {}", showRowIndex, e.getMessage(), e);
            }
        }

        if (globalMessage.hasData()) {
            record.setImportMessage(globalMessage.toJSON());
            record.setImportState(ImportStateEnum.Failed);
            iProductImportRecordService.updateById(record);
        } else {
            Collection<ProductImportBo> productImportBoList = productGroupMap.values();
            try {
                productSupport.saveProductImport(tenantId, productImportBoList);
                record.setImportState(ImportStateEnum.Success);
                record.setImportProducts(CollUtil.size(productImportBoList));
            } catch (DataIntegrityViolationException e) {
                log.error("保存导入商品出现数据库异常，原因 {}", e.getMessage(), e);
                LocaleMessage localeMessage = LocaleMessage.byStatusCode(ZSMallStatusCodeEnum.SAVE_IMPORT_DATA_ERROR);
                Throwable cause = e.getCause();
                if (cause != null) {
                    String message = cause.getMessage();
                    if (StrUtil.contains(message, "Data too long for column")) {
                        localeMessage = LocaleMessage.byStatusCode(ZSMallStatusCodeEnum.DATABASE_DATA_TOO_LONG);
                    }
                }
                record.setImportMessage(localeMessage.toJSON());
                record.setImportState(ImportStateEnum.Failed);
            } catch (Exception e) {
                log.error("保存导入商品出现未知异常，原因 {}", e.getMessage(), e);
                record.setImportMessage(LocaleMessage.byStatusCodeToJSON(ZSMallStatusCodeEnum.SAVE_IMPORT_DATA_ERROR));
                record.setImportState(ImportStateEnum.Failed);
            }
            iProductImportRecordService.updateById(record);
        }
        return R.ok();
    }

    /**
     * 获取商品SKU简单信息
     *
     * @param bo
     * @param pageQuery
     * @return
     */
    @Override
    public TableDataInfo<ProductSkuSimpleVo> getProductSkuPage(ProductPriceBo bo, PageQuery pageQuery) {
        String queryType = bo.getQueryType();
        String queryValue = bo.getQueryValue();

        if (StrUtil.isBlank(queryType) || StrUtil.isBlank(queryValue)) {
            queryType = null;
            queryValue = null;
        }
        queryType = StrUtil.isBlank(queryType) ? null : StrUtil.trim(queryType);
        queryValue = StrUtil.isBlank(queryValue) ? null : StrUtil.trim(queryValue);

        String finalQueryType = queryType;
        String finalQueryValue = queryValue;
        Page<ProductSkuSimpleVo> productSkuPage = iProductSkuService.getPageForQA(LoginHelper.getTenantId() ,finalQueryType, finalQueryValue, pageQuery.build());
        // List<ProductSkuSimpleVo> records = productSkuPage.getRecords();
        //
        // List<ProductSkuSimpleVo> skuSimpleVos = new ArrayList<>();
        // if (CollUtil.isNotEmpty(records)) {
        //     records.forEach(record -> {
        //
        //         String productCode = record.getProductCode();
        //         String productSkuCode = record.getProductSkuCode();
        //         String sku = record.getSku();
        //         String name = record.getName();
        //
        //         ProductSkuSimpleVo skuSimpleVo = new ProductSkuSimpleVo(productCode, productSkuCode, name, sku);
        //
        //         ProductSkuAttachmentVo productSkuAttachmentVo = iProductSkuAttachmentService.queryFirstImageByProductSkuCode(productSkuCode);
        //         skuSimpleVo.setProductSkuAttachment(productSkuAttachmentVo);
        //
        //         skuSimpleVos.add(skuSimpleVo);
        //     });
        // }

        return TableDataInfo.build(productSkuPage);
    }

    /**
     * 获取自定义导出商品信息可选字段（管理员）
     */
    @Override
    public R<List<CustomExportFieldVo>> getCustomExportFields() {
        LoginUser loginUser = LoginHelper.getLoginUser(TenantType.Manager);
        List<CustomExportFieldVo> voList = Arrays.stream(ProductExportFieldEnum.values()).map(fieldEnum -> new CustomExportFieldVo(fieldEnum.name(), fieldEnum.getZh_CN(), fieldEnum.getEn_US())).collect(Collectors.toList());
        return R.ok(voList);
    }

    /**
     * 自定义导出商品信息（管理员）
     *
     * @param bo
     */
    @Override
    public R<Void> customExport(CustomExportBo bo) {
        LoginUser loginUser = LoginHelper.getLoginUser(TenantType.Manager);
        String headerLanguage = ServletUtils.getHeaderLanguage();
        log.info("headerLanguage => {}", headerLanguage);

        Boolean existsed = iDownloadRecordService.existsByRecordState(RecordStateEnum.Generating);
        if (existsed) {
            return R.fail(ZSMallStatusCodeEnum.DOWNLOAD_RECORD_GENERATING);
        } else {
            String fileName = StrUtil.format(FileNameConstants.PRODUCT_CUSTOM_EXPORT, DateUtil.format(new Date(), "yyMMdd-HH:mm:ss.SSSS"));

            // 创建新的下载记录
            DownloadRecord newRecord = new DownloadRecord();
            newRecord.setRecordState(RecordStateEnum.Generating);
            newRecord.setFileName(fileName);
            newRecord.setDownloadType(DownloadTypePlusEnum.ProductCustomExport);
            iDownloadRecordService.save(newRecord);

            ThreadUtil.execute(() -> {
                BigExcelWriter excelWriter = null;

                try {
                    List<ProductCustomExportVo> customExportVoList = iProductSkuService.queryCustomExportList(bo);
                    List<String> exportFieldList = bo.getExportFieldList();
                    List<ProductExportFieldEnum> fieldEnums = ProductExportFieldEnum.valueOfByList(exportFieldList);

                    List<Map<String, Object>> rows = new ArrayList<>();
                    for (ProductCustomExportVo productCustomExportVo : customExportVoList) {
                        Map<String, Object> map = new LinkedHashMap<>();
                        for (ProductExportFieldEnum fieldEnum : fieldEnums) {
                            Object fieldValue;
                            if (ProductExportFieldEnum.productLink.equals(fieldEnum)) {
                                String productType = productCustomExportVo.getProductType();
                                String configValue;
                                if (ProductTypeEnum.WholesaleProduct.name().equals(productType)) {
                                    configValue = SystemEventUtils.getSysConfigValue(MallConstants.PREFIX_CONFIG + MallConstants.Config.WHOLESALE_PRODUCT_LINK);
                                } else {
                                    configValue = SystemEventUtils.getSysConfigValue(MallConstants.PREFIX_CONFIG + MallConstants.Config.NORMAL_PRODUCT_LINK);
                                }

                                fieldValue = StrUtil.format(configValue, productCustomExportVo.getProductCode());
                            } else {
                                fieldValue = ReflectUtil.invoke(productCustomExportVo, "get" + StrUtil.upperFirst(fieldEnum.name()));
                                if (fieldValue instanceof Enum) {
                                    Method method = ReflectUtil.getMethod(ProductCustomExportVo.class, "get" + StrUtil.upperFirst(fieldEnum.name()), String.class);
                                    if (method != null) {
                                        fieldValue = ReflectUtil.invoke(productCustomExportVo, method, headerLanguage);
                                    }
                                }
                            }
                            map.put(fieldEnum.getFieldName(headerLanguage), fieldValue);
                        }
                        rows.add(map);
                    }
                    // log.info("rows => {}", JSONUtil.toJsonPrettyStr(rows));

                    String tempSavePath = fileProperties.getTempSavePath();
                    String tempFile = tempSavePath + File.separator + UUID.fastUUID().toString(true) + ".xlsx";

                    excelWriter = ExcelUtil.getBigWriter();
                    excelWriter.write(rows, true);
                    excelWriter.autoSizeColumnAll();
                    File file = FileUtil.newFile(tempFile);
                    excelWriter.flush(file);

                    @Cleanup InputStream inputStream = FileUtil.getInputStream(file);
                    OSSUploadEvent uploadEvent = new OSSUploadEvent(inputStream, fileName);
                    SpringUtils.publishEvent(uploadEvent);
                    SysOssVo sysOssVo = uploadEvent.getSysOssVo();

                    newRecord.setOssId(sysOssVo.getOssId());
                    newRecord.setFileSaveKey(sysOssVo.getFileName());
                    newRecord.setFileUrl(sysOssVo.getUrl());
                    newRecord.setFileSize(StrUtil.toString(file.length()));
                    newRecord.setRecordState(RecordStateEnum.Ready);
                    iDownloadRecordService.updateById(newRecord);
                    FileUtil.del(file);
                } catch (Exception e) {
                    log.error("【自定义导出商品信息】出现未知错误 {}", e.getMessage(), e);
                    newRecord.setRecordState(RecordStateEnum.Failed);
                    iDownloadRecordService.updateById(newRecord);
                }
            });
        }
        return R.ok();
    }

    //    @Override
//    public void export(ProductQueryBo bo, PageQuery pageQuery, HttpServletResponse response) {
//        try {
//            String tenantId = LoginHelper.getTenantId();
//            bo.setProductType(ProductTypeEnum.NormalProduct.name());
//            pageQuery.setPageNum(1);
//            pageQuery.setPageSize(1);
//            Integer rowCount = iProductService.getBaseMapper().getProductExportCount(bo,tenantId);
//            if (rowCount == 0) {
//                com.hengjian.common.excel.utils.ExcelUtil.exportExcel(new ArrayList<>(), "商品导出", ProductExportDto.class, response, Boolean.FALSE);
//                return;
//            } else {
//                try {
//                    com.hengjian.common.excel.utils.ExcelUtil.resetResponse("商品导出", response, false);
//                } catch (UnsupportedEncodingException e) {
//                    throw new RuntimeException(e);
//                }
//            }
//            ConcurrentLinkedQueue<ProductExportDto> productExportDtoList = new ConcurrentLinkedQueue<>();
//            // 一次查询的数据量
//            int pageSize = 200;
//            // 总页数
//            int totalPage = rowCount % pageSize == 0 ? rowCount / pageSize : (rowCount / pageSize + 1);
//            ServletOutputStream singleOutputStream = response.getOutputStream();
//            ExcelWriter excelWriter = EasyExcel.write(singleOutputStream,ProductExportDto.class).build();
//            CountDownLatch pageThreadSignal = new CountDownLatch(totalPage);
//            for (int currentPage = 1; currentPage <= totalPage; currentPage++) {
//                // 计算当前页的起始行
//                int startRow = (currentPage - 1) * pageSize;
//                // 如果是最后一页且不足pageSize条数据，调整pageSize为剩余的数据量
//                if (currentPage == totalPage && rowCount % pageSize != 0) {
//                    pageSize = rowCount % pageSize;
//                }
//                int finalPageSize = pageSize;
//                ioThreadPoolExecutor.execute(() -> {
//                    try {
//                        List<ProductExportDto> pageData = writeProductExport(startRow, finalPageSize, bo, excelWriter,tenantId);
//                        productExportDtoList.addAll(pageData);
//                    } catch (Exception e) {
//                        log.error("数据处理异常{}", e.getMessage());
//                    } finally {
//                        pageThreadSignal.countDown();
//                    }
//                });
//            }
//            try {
//                pageThreadSignal.await();
//            } catch (InterruptedException e) {
//                Thread.currentThread().interrupt();
//            }
//            WriteSheet writeSheet = EasyExcel.writerSheet(0, "商品导出")
//                                             .head(ProductExportDto.class)
//                                             .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
//                                             .registerConverter(new ExcelBigNumberConvert())
//                                             .build();
//            List<ProductExportDto> sortedData = new ArrayList<>(productExportDtoList);
//            sortedData.sort(Comparator.comparing(ProductExportDto::getProductCreateTime).reversed());
//            excelWriter.write(sortedData, writeSheet);
//            excelWriter.finish();
//        }catch (Exception e){
//            log.info(e.getMessage());
//        }
//    }
    private void exportEmptyExcel(HttpServletResponse response, String sheetName, Class<?> clazz) {
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-Disposition", "attachment;filename*=utf-8''空数据.xlsx");

            ExcelWriter excelWriter = EasyExcel.write(outputStream, clazz).build();
            excelWriter.write(new ArrayList<>(), EasyExcel.writerSheet(sheetName).build());
            excelWriter.finish();
        } catch (IOException e) {
            log.error("导出空 Excel 时发生 IO 异常", e);
        }
    }
    @Override
    public void exportAsync(ProductQueryBo bo, PageQuery pageQuery, HttpServletResponse response) {
        String tenantId = LoginHelper.getTenantId();
        Integer rowCount = iProductService.getBaseMapper().getExportInventoryDimensionCount(bo, tenantId);
        if(rowCount==0){
            throw new RuntimeException("当前查询条件无商品数据,请重新输入");
        }
        Locale locale = ServletUtils.getHeaderLocale();
        LoginUser loginUser = LoginHelper.getLoginUser();
        new Thread(() -> {
            try {
                exportAsync(bo, response, locale,loginUser);
            } catch (Exception e) {
                // 处理异常，记录日志或执行其他恢复操作
                log.error("导出商品列表异常", e);
            }
        }).start();
    }

    @Override
    public void export(ProductQueryBo bo, PageQuery pageQuery, HttpServletResponse response) {
        String tenantId = LoginHelper.getTenantId();
        Integer rowCount = iProductService.getBaseMapper().getExportInventoryDimensionCount(bo, tenantId);
        if(rowCount==0){
            throw new RuntimeException("当前查询条件无商品数据,请重新输入");
        }
        exportNotAsync(bo, response);
    }

    private void exportAsync(ProductQueryBo bo, HttpServletResponse response, Locale locale, LoginUser loginU) {
        Runnable task = () -> {
            try {
                Locale headerLocale = LoginContextLocalHolder.getLocaleInfo();

                LoginUser loginUser = LoginContextLocalHolder.getLoginInfo();
                String tenantId = loginUser.getTenantId();
                String fileName = StrUtil.format(FileNameConstants.PRODUCT_LIST_EXPORT, DateUtil.format(new Date(), "yyMMdd-HHmmssSS"));
                DownloadRecordV2Util.generate(fileName, DownloadTypePlusEnum.ProductListExport, tempFileSavePath -> {
                    bo.setProductType(ProductTypeEnum.NormalProduct.name());
                    ConcurrentLinkedQueue<ProductExportDto> productExportDtoList = new ConcurrentLinkedQueue<>();
                    try {
                        // 线上目前最大导出数据量为 580896 条 , 后续为了防止内存溢出，可以分页导出,查询压力并不大
                        List<ProductExportDto> pageData = writeProductExportV2(bo, tenantId);
                        productExportDtoList.addAll(pageData);
                    } catch (Exception e) {
                        log.error("数据处理异常",e);
                    }

                    List<ProductExportDto> sortedData = new ArrayList<>(productExportDtoList);

                    File tempFile = new File(tempFileSavePath);
                    BufferedOutputStream outputStream = FileUtil.getOutputStream(tempFile);
                    com.hengjian.common.excel.utils.ExcelUtil.exportExcelWithLocale(sortedData, "Product list", ProductExportDto.class, false, outputStream, headerLocale);
                    IoUtil.close(outputStream);
                    return tempFile;
                });
            } catch (Exception e) {
                // 处理异常，记录日志或执行其他恢复操作
                log.error("导出商品列表异常", e);
            }
        };
        HeaderLocaleRunnable runnable = new HeaderLocaleRunnable(loginU, task, locale);
        executor.submit(runnable);
        try {
            executor.awaitTermination(10, TimeUnit.MINUTES);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    private void exportNotAsync(ProductQueryBo bo, HttpServletResponse response) {
        try {
            Locale headerLocale = ServletUtils.getHeaderLocale();

            String tenantId = LoginHelper.getTenantId();
            String fileName = StrUtil.format(FileNameConstants.PRODUCT_LIST_EXPORT, DateUtil.format(new Date(), "yyMMdd-HHmmssSS"));
            DownloadRecordV2Util.generate(fileName, DownloadTypePlusEnum.ProductListExport, tempFileSavePath -> {
                bo.setProductType(ProductTypeEnum.NormalProduct.name());
                ConcurrentLinkedQueue<ProductExportDto> productExportDtoList = new ConcurrentLinkedQueue<>();
                try {
                    List<ProductExportDto> pageData = writeProductExportV2(bo, tenantId);
                    productExportDtoList.addAll(pageData);
                } catch (Exception e) {
                    log.error("数据处理异常:", e);
                }

                List<ProductExportDto> sortedData = new ArrayList<>(productExportDtoList);

                File tempFile = new File(tempFileSavePath);
                BufferedOutputStream outputStream = FileUtil.getOutputStream(tempFile);
                com.hengjian.common.excel.utils.ExcelUtil.exportExcelWithLocale(sortedData, "Product list", ProductExportDto.class, false, outputStream, headerLocale);
                IoUtil.close(outputStream);
                return tempFile;
            });
        } catch (Exception e) {
            // 处理异常，记录日志或执行其他恢复操作
            log.error("导出商品列表异常", e);
        }
    }

    /**
     * 功能描述：写入产品导出v2 单次查询
     *
     * @param bo       bo
     * @param tenantId 租户id
     * @return {@link List }<{@link ProductExportDto }>
     * <AUTHOR>
     * @date 2024/12/03
     */
    private List<ProductExportDto> writeProductExportV2(ProductQueryBo bo, String tenantId) {
        List<ProductExportDto> records = TenantHelper.ignore(()->iProductService.getBaseMapper().queryExportInventoryDimensionV2(bo,tenantId));
        if(CollUtil.isEmpty(records)){
            return new ArrayList<>();
        }
        Set<Long> productSkuId = records.stream().map(ProductExportDto::getProductSkuId).collect(Collectors.toSet());
        Set<String> warehouseSystemCodes = records.stream()
                                                  .map(ProductExportDto::getWarehouseSystemCode)
                                                  .filter(Objects::nonNull) // 排除 null 值
                                                  .collect(Collectors.toSet());
        //查询Product_sku_price
        LambdaQueryWrapper<ProductSkuPrice> productSkuPriceWrapper = new LambdaQueryWrapper<>();
        productSkuPriceWrapper.in(CollUtil.isNotEmpty(productSkuId),ProductSkuPrice::getProductSkuId, productSkuId).eq(ProductSkuPrice::getDelFlag,0);
        List<ProductSkuPrice> productSkuPrices=  TenantHelper.ignore(()-> iProductSkuPriceService.getBaseMapper()
                                                                                                 .selectList(productSkuPriceWrapper));

        if(CollUtil.isEmpty(warehouseSystemCodes)){
            log.info("商品导出，仓库为空");
        }
        LambdaQueryWrapper<Warehouse> queryWrapper = new LambdaQueryWrapper<Warehouse>().in(Warehouse::getWarehouseSystemCode, warehouseSystemCodes).eq(Warehouse::getDelFlag,0).eq(Warehouse::getTenantId,tenantId);
        List<Warehouse> warehouses = TenantHelper.ignore(()->iWarehouseService.list(queryWrapper));
        Map<String, String> warehouseNameMap = new HashMap<>();
        Map<String, String> warehouseAdrdessMap = new HashMap<>();
        Map<String, String> addressWarehouseMap = new HashMap<>();
        if(CollUtil.isNotEmpty(warehouses)){
            for (Warehouse warehouse : warehouses) {
                if(ObjectUtil.isNotEmpty(warehouse)){
                    if(StrUtil.isNotEmpty(warehouse.getWarehouseName())){
                        warehouseNameMap.put(warehouse.getWarehouseSystemCode(),warehouse.getWarehouseName());
                    }else {
                        warehouseNameMap.put(warehouse.getWarehouseSystemCode(),"");
                    }
                }
            }

        }


        LambdaQueryWrapper<WarehouseAddress> addressLambdaQueryWrapper = new LambdaQueryWrapper<WarehouseAddress>().in(WarehouseAddress::getWarehouseSystemCode, warehouseSystemCodes)
                                                                                                                   .eq(WarehouseAddress::getDelFlag, 0);

        List<WarehouseAddress> warehouseAddresses = TenantHelper.ignore(()->iWarehouseAddressService.list(addressLambdaQueryWrapper)) ;
        if(CollUtil.isNotEmpty(warehouseAddresses)){
            for (WarehouseAddress warehouseAddress : warehouseAddresses) {
                if(ObjectUtil.isNotEmpty(warehouseAddress)){
                    if(StrUtil.isNotEmpty(warehouseAddress.getAddress2())){
                        warehouseAdrdessMap.put(warehouseAddress.getWarehouseSystemCode(),warehouseAddress.getAddress2());
                    }else {
                        warehouseAdrdessMap.put(warehouseAddress.getWarehouseSystemCode(),"");
                    }

                }
            }

            for (WarehouseAddress wa : warehouseAddresses) {
                if (ObjectUtil.isNotNull(wa.getWarehouseSystemCode())) {
                    StringBuilder addressParts = new StringBuilder();
                    boolean isFirst = true;
                    for (String part : new String[]{wa.getAddress1(), wa.getCity(), wa.getState(), wa.getZipCode(), wa.getCountry()}) {
                        if (Objects.nonNull(part)) {
                            if (!isFirst) {
                                addressParts.append(",");
                            }
                            addressParts.append(part);
                            isFirst = false;
                        }
                    }
                    addressWarehouseMap.put(wa.getWarehouseSystemCode(), addressParts.toString());
                }
            }
        }

        //productSkuPrices转Map key 是productSkuId+siteId+ value是对应的元素
        Map<String, ProductSkuPrice> productSkuPriceMap = productSkuPrices.stream()
                                                                          .collect(Collectors.toMap(
                                                                              productSkuPrice -> productSkuPrice.getProductSkuId() + "-" + productSkuPrice.getSiteId(),
                                                                              productSkuPrice -> productSkuPrice
                                                                          ));
        //查询规格信息
        LambdaQueryWrapper<ProductSkuDetail> productSkuDetailLQW = new LambdaQueryWrapper<>();
        productSkuDetailLQW.in(ProductSkuDetail::getProductSkuId,productSkuId);
        List<ProductSkuDetail> productSkuDetailList = TenantHelper.ignore(()->iProductSkuDetailService.getBaseMapper().selectList(productSkuDetailLQW));
        Map<Long, ProductSkuDetail> productSkuDetailMap = productSkuDetailList.stream()
                                                                              .collect(Collectors.toMap(ProductSkuDetail::getProductSkuId, productSkuDetail -> productSkuDetail));

        //获取上下架状态
        Map<String, String> skuShelfStateMap = getSkuShelfStateMap();
        //组装表格信息
        for (ProductExportDto productExportDto : records) {
            productExportDto.setSkuShelfState(skuShelfStateMap.getOrDefault(productExportDto.getSkuShelfState(), "未知"));
            if(CollUtil.isNotEmpty(warehouseNameMap)){
                productExportDto.setWarehouseName(warehouseNameMap.get(productExportDto.getWarehouseSystemCode()));
            }
            if (CollUtil.isNotEmpty(addressWarehouseMap)){
                productExportDto.setWarehouseAddress(addressWarehouseMap.get(productExportDto.getWarehouseSystemCode()));
            }

            if(CollUtil.isNotEmpty(warehouseAdrdessMap)){
                productExportDto.setWarehouseAddressDetail(warehouseAdrdessMap.get(productExportDto.getWarehouseSystemCode()));
            }

            //获取价格信息
            ProductSkuPrice productSkuPrice = productSkuPriceMap.get(productExportDto.getProductSkuId()+"-"+productExportDto.getSiteId());
            if (ObjectUtil.isNotNull(productSkuPrice)){
                //判断商品发货类型
                String supportedLogistics = productExportDto.getSupportedLogistics();
                //自提
                if (SupportedLogisticsEnum.PickUpOnly.name().equals(supportedLogistics)){
                    //供应商的价格
                    productExportDto.setPickUpPrice(String.valueOf(productSkuPrice.getOriginalPickUpPrice()));
                    productExportDto.setDropShippingPrice("不支持");
                    //代发
                }else if (SupportedLogisticsEnum.DropShippingOnly.name().equals(supportedLogistics)){
                    productExportDto.setDropShippingPrice(String.valueOf(productSkuPrice.getOriginalDropShippingPrice()));
                    productExportDto.setPickUpPrice("不支持");
                }else {
                    productExportDto.setDropShippingPrice(String.valueOf(productSkuPrice.getOriginalDropShippingPrice()));
                    productExportDto.setPickUpPrice(String.valueOf(productSkuPrice.getOriginalPickUpPrice()));
                }
            }
            productExportDto.setVerifyState(getSkuVerifyStateMap().getOrDefault(productExportDto.getVerifyState(), "未知"));
            // 设置一件代发库存
            if(null != productExportDto.getDropShippingStockAvailable() && productExportDto.getDropShippingStockAvailable() == 1){
                productExportDto.setProxyStockTotal(productExportDto.getStockAvailable());
            }else {
                productExportDto.setProxyStockTotal("0");
            }
            //设置商品规格信息
            ProductSkuDetail productSkuDetail = productSkuDetailMap.get(productExportDto.getProductSkuId());
            if (ObjectUtil.isNotNull(productSkuDetail)){
                productExportDto.setProductSize(productSkuDetail.getLength()+"x"+productSkuDetail.getWidth()+"x"+productSkuDetail.getHeight()+" "+productSkuDetail.getLengthUnit().name());
                productExportDto.setProductWeight(productSkuDetail.getWeight()+" "+productSkuDetail.getWeightUnit().name());
                productExportDto.setPackageSize(productSkuDetail.getPackLength()+"x"+productSkuDetail.getPackWidth()+"x"+productSkuDetail.getPackHeight()+" "+productSkuDetail.getPackLengthUnit().name());
                productExportDto.setPackageWeight(productSkuDetail.getPackWeight()+" "+productSkuDetail.getPackWeightUnit().name());
            }

        }

        return  records;
    }

    @InMethodLog(value = "sku上下架")
    @Override
    @Transactional
    public void skuShelf(ProductSkuShelfBo bo) {
        Product product = iProductService.queryById(bo.getProductId());
        if (product == null){
            log.info("sku上下架，产品为空,{}",bo);
            return;
        }
        ProductSku productSku = iProductSkuService.queryById(bo.getProductSkuId());
        if(null == productSku){
            log.info("sku上下架，产品sku为空,{}",bo);
            return;
        }
        if(bo.getIsOnShelf()){
            productSku.setShelfState(ShelfStateEnum.OnShelf);
        }else{
            productSku.setShelfState(ShelfStateEnum.OffShelf);
        }
        productSku.setUpdateBy(LoginHelper.getUserId());
        // 修改商品的上下架状态
        iProductSkuService.updateById(productSku);
        //更新SPU上下架状态
        boolean allSkuOffShelfBySpu = iProductService.isAllSkuOffShelfBySpu(product.getProductCode());
        product.setShelfState(allSkuOffShelfBySpu ?ShelfStateEnum.OffShelf:ShelfStateEnum.OnShelf);
        iProductService.updateById(product);
        // 将产品上传至es中，刷新sku的状态
        esProductSupport.productUpload(product);
    }

    @Override
    public void manualProductPush(String productCode) {
        LambdaQueryWrapper<Product> wrapper = new LambdaQueryWrapper<Product>().eq(Product::getProductCode, productCode).eq(Product::getDelFlag,0);
        Product product = iProductService.getOne(wrapper);
        try{
            esProductSupport.productUpload(product);
        }catch (Exception e){
            log.error("商品上传es失败",e);
            throw new RuntimeException("商品上传es失败");
        }

    }
    private  List<ProductExportDto> writeProductExport(Integer page, Integer pagesize,ProductQueryBo bo,ExcelWriter excelWriter,String tenantId) {
        List<ProductExportDto> records = iProductService.getBaseMapper().queryExportInventoryDimension(page,pagesize, bo,tenantId);
        if(CollUtil.isEmpty(records)){
            log.info("商品导出，仓库为空");
        }
        Set<Long> productSkuId = records.stream().map(ProductExportDto::getProductSkuId).collect(Collectors.toSet());
        Set<String> warehouseSystemCodes = records.stream().map(ProductExportDto::getWarehouseSystemCode)
                                     .collect(Collectors.toSet());
        //查询Product_sku_price
        LambdaQueryWrapper<ProductSkuPrice> productSkuPriceWrapper = new LambdaQueryWrapper<>();
        productSkuPriceWrapper.in(CollUtil.isNotEmpty(productSkuId),ProductSkuPrice::getProductSkuId, productSkuId);
        List<ProductSkuPrice> productSkuPrices=  TenantHelper.ignore(()-> iProductSkuPriceService.getBaseMapper()
                                                        .selectList(productSkuPriceWrapper));

        //
        if(CollUtil.isEmpty(warehouseSystemCodes)){
            log.info("商品导出，仓库为空");
        }
        Map<String, String> warehouseNameMap = new HashMap<>();
        LambdaQueryWrapper<Warehouse> queryWrapper = new LambdaQueryWrapper<Warehouse>().in(Warehouse::getWarehouseSystemCode, warehouseSystemCodes).eq(Warehouse::getDelFlag,0).eq(Warehouse::getTenantId,tenantId);
        List<Warehouse> warehouses = TenantHelper.ignore(()->iWarehouseService.list(queryWrapper));
        for (Warehouse warehouse : warehouses) {
            if(ObjectUtil.isNotEmpty(warehouse.getWarehouseName())){
                warehouseNameMap.put(warehouse.getWarehouseSystemCode(),warehouse.getWarehouseName());
            }else {
                warehouseNameMap.put(warehouse.getWarehouseSystemCode(),"");
            }
        }


        LambdaQueryWrapper<WarehouseAddress> addressLambdaQueryWrapper = new LambdaQueryWrapper<WarehouseAddress>().in(WarehouseAddress::getWarehouseSystemCode, warehouseSystemCodes)
                                                                                                                   .eq(WarehouseAddress::getDelFlag, 0);

        List<WarehouseAddress> warehouseAddresses = TenantHelper.ignore(()->iWarehouseAddressService.list(addressLambdaQueryWrapper)) ;
        Map<String, String> addressWarehouseMap = new HashMap<>();
        for (WarehouseAddress warehouseAddress : warehouseAddresses) {
            if(ObjectUtil.isNotEmpty(warehouseAddress)){
                if(ObjectUtil.isNotEmpty(warehouseAddress.getAddress2())){
                    addressWarehouseMap.put(warehouseAddress.getWarehouseSystemCode(),warehouseAddress.getAddress2());
                }else {
                    addressWarehouseMap.put(warehouseAddress.getWarehouseSystemCode(),"");
                }

            }
        }

        //转Map
        Map<Long, ProductSkuPrice> productSkuPriceMap = productSkuPrices.stream()
                                                                        .collect(Collectors.toMap(ProductSkuPrice::getProductSkuId, productSkuPrice -> productSkuPrice));
        //获取上下架状态
        Map<String, String> skuShelfStateMap = getSkuShelfStateMap();
        //组装表格信息
        records.forEach(productExportDto -> {
            productExportDto.setSkuShelfState(skuShelfStateMap.getOrDefault(productExportDto.getSkuShelfState(), "未知"));
            productExportDto.setWarehouseName(warehouseNameMap.get(productExportDto.getWarehouseSystemCode()));
            productExportDto.setWarehouseAddress(addressWarehouseMap.get(productExportDto.getWarehouseSystemCode()));
            //获取价格信息
            ProductSkuPrice productSkuPrice = productSkuPriceMap.get(productExportDto.getProductSkuId());
            if (ObjectUtil.isNotNull(productSkuPrice)){
                //判断商品发货类型
                String supportedLogistics = productExportDto.getSupportedLogistics();
                //自提
                if (SupportedLogisticsEnum.PickUpOnly.name().equals(supportedLogistics)){
                    //供应商的价格
                    productExportDto.setPickUpPrice(String.valueOf(productSkuPrice.getOriginalPickUpPrice()));
                    productExportDto.setDropShippingPrice("不支持");
                    //代发
                }else if (SupportedLogisticsEnum.DropShippingOnly.name().equals(supportedLogistics)){
                    productExportDto.setDropShippingPrice(String.valueOf(productSkuPrice.getOriginalDropShippingPrice()));
                    productExportDto.setPickUpPrice("不支持");
                }else {
                    productExportDto.setDropShippingPrice(String.valueOf(productSkuPrice.getOriginalDropShippingPrice()));
                    productExportDto.setPickUpPrice(String.valueOf(productSkuPrice.getOriginalPickUpPrice()));
                }
            }
            productExportDto.setVerifyState(getSkuVerifyStateMap().getOrDefault(productExportDto.getVerifyState(), "未知"));
        });
        return  records;
    }

    /**
     * 获取上下架状态
     * @return
     */
    public Map<String,String> getSkuShelfStateMap(){
        Map<String, String> shelfStateMap = new HashMap<>();
        shelfStateMap.put("OnShelf", "上架");
        shelfStateMap.put("OffShelf", "下架");
        shelfStateMap.put("ForcedOffShelf", "未审核");
        return  shelfStateMap;
    }

    /**
     * 获取审核状态
     * @return
     */
    public Map<String,String> getSkuVerifyStateMap(){
        Map<String, String> verifyStateMap = new HashMap<>();
        verifyStateMap.put("Draft", "草稿");
        verifyStateMap.put("Pending", "审核中");
        verifyStateMap.put("Accepted", "已通过");
        verifyStateMap.put("Rejected", "已驳回");
        verifyStateMap.put("Abandoned", "废弃");
        return  verifyStateMap;
    }

    private void generateReviewRecordOrUpdatePrice(ProductSku productSku, ProductSkuPrice newProductSkuPrice,
                                                   Long siteId, Map<Long, SiteCountryCurrency> siteIdMap) throws RStatusCodeException {

        log.info("进入【提交审核或者直接更新价格】方法， productSkuId = {}, newProductSkuPrice = {} ", productSku.getProductId(), JSONUtil.toJsonStr(newProductSkuPrice));
        String productSkuCode = productSku.getProductSkuCode();
        String productCode = productSku.getProductCode();
        ShelfStateEnum skuShelfState = productSku.getShelfState();
        Product product = iProductService.queryByProductCode(productCode);
        Long productSkuId = productSku.getId();
        SupportedLogisticsEnum supportedLogistics = product.getSupportedLogistics();
        ProductVerifyStateEnum verifyState = product.getVerifyState();
        SiteCountryCurrency currency = siteIdMap.get(siteId);

        String countryCode = currency.getCountryCode();
        String currencyCode = currency.getCurrencyCode();
        String currencySymbol = currency.getCurrencySymbol();


        // SPU数据变更
        List<ChangeFieldDTO> SPUChangeFields = new ArrayList<>();
        SPUChangeFields.add(new ChangeFieldDTO("supportedLogistics", supportedLogistics.name(),
            supportedLogistics.name()));
        //审核中的商品无法修改信息
        if (ObjectUtil.equals(verifyState, ProductVerifyStateEnum.Pending)) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.NEW_PRODUCT_PRICE_VERIFY_PENDING);
        }

        //查询审核记录，商品价格是否处于审核中
        Boolean exists = iProductReviewRecordService.existsByProductSkuCode(productSkuCode, ProductReviewTypeEnum.Price,
            ProductVerifyStateEnum.Pending);
        if (exists) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.NEW_PRODUCT_PRICE_VERIFY_PENDING);
        }

        // 已存在的SKU价格变更审核DTO
        List<ProductSkuReviewDTO> productSkuReviewList = new ArrayList<>();
        // 新的SKU价格变更审核DTO
        List<ProductSkuReviewDTO> newProductSkuReviewList = new ArrayList<>();

        BigDecimal originalUnitPrice = newProductSkuPrice.getOriginalUnitPrice();
        BigDecimal originalOperationFee = newProductSkuPrice.getOriginalOperationFee();
        BigDecimal originalFinalDeliveryFee = newProductSkuPrice.getOriginalFinalDeliveryFee();
        BigDecimal msrp = newProductSkuPrice.getMsrp();
        // todo 先统计,要改动
        ProductSkuPrice productSkuPrice = iProductSkuPriceService.getById(newProductSkuPrice.getId());
        ProductSkuPrice matchRule = iProductSkuPriceRuleService.matchRule(productSkuCode, originalUnitPrice, originalOperationFee, originalFinalDeliveryFee, false);
        Long newPriceRuleId = matchRule.getProductSkuPriceRuleId();

        BigDecimal beforeMsrp = productSkuPrice.getMsrp();
        BigDecimal beforeUnitPrice = productSkuPrice.getOriginalUnitPrice();
        BigDecimal beforeOperationFee = productSkuPrice.getOriginalOperationFee();
        BigDecimal beforeFinalDeliveryFee = productSkuPrice.getOriginalFinalDeliveryFee();
        BigDecimal beforePickUpPrice = productSkuPrice.getOriginalPickUpPrice();
        BigDecimal beforeDropShippingPrice = productSkuPrice.getOriginalDropShippingPrice();

        BigDecimal originalPickUpPrice = matchRule.getOriginalPickUpPrice();
        BigDecimal originalDropShippingPrice = matchRule.getOriginalDropShippingPrice();
        BigDecimal unitPriceMd = matchRule.getPlatformUnitPrice();
        BigDecimal operationFeeMd = matchRule.getPlatformOperationFee();
        BigDecimal finalDeliveryFeeMd = matchRule.getPlatformFinalDeliveryFee();
        BigDecimal pickUpPriceMd = matchRule.getPlatformPickUpPrice();
        BigDecimal dropShippingPriceMd = matchRule.getPlatformDropShippingPrice();

        ProductVerifyStateEnum skuVerifyState = productSku.getVerifyState();
        // 但Sku未过审或被拒绝时
        if (ProductVerifyStateEnum.Draft.equals(skuVerifyState) || ProductVerifyStateEnum.Rejected.equals(skuVerifyState)) {
            productSkuPrice.setMsrp(msrp);
            productSkuPrice.setOriginalUnitPrice(originalUnitPrice);
            productSkuPrice.setOriginalOperationFee(originalOperationFee);
            productSkuPrice.setOriginalFinalDeliveryFee(originalFinalDeliveryFee);
            productSkuPrice.setOriginalPickUpPrice(originalPickUpPrice);
            productSkuPrice.setOriginalDropShippingPrice(originalDropShippingPrice);
            productSkuPrice.setPlatformUnitPrice(unitPriceMd);
            productSkuPrice.setPlatformOperationFee(operationFeeMd);
            productSkuPrice.setPlatformFinalDeliveryFee(finalDeliveryFeeMd);
            productSkuPrice.setPlatformPickUpPrice(pickUpPriceMd);
            productSkuPrice.setPlatformDropShippingPrice(dropShippingPriceMd);

            productSkuPrice.setSiteId(siteId);
            productSkuPrice.setCurrency(currencyCode);
            productSkuPrice.setCurrencySymbol(currencySymbol);
            productSkuPrice.setCountryCode(countryCode);
            //如果价格信息已存在，则原价存入价格日志表，新价格存入价格表
            if (ObjectUtil.isNotNull(productSkuPrice.getId())) {
                //记录价格日志
                iProductSkuPriceLogService.recordPriceChanges(productSkuPrice,  PriceOperateLog.Update.name());
            }

            // 记录变更字段及值
            ProductSkuReviewDTO productSkuReviewDTO = new ProductSkuReviewDTO();
            productSkuReviewDTO.setProductSkuCode(productSkuCode);

            productSkuReviewDTO.setSiteId(siteId);
            productSkuReviewDTO.setProductSkuPriceId(productSkuPrice.getId());
            productSkuReviewDTO.setReviewType(PriceOperateLog.Update.name());

            productSkuReviewDTO.addField("msrp", NumberUtil.toStr(beforeMsrp), NumberUtil.toStr(msrp, "0"));
            productSkuReviewDTO.addField("pickUpPrice", NumberUtil.toStr(beforePickUpPrice), NumberUtil.toStr(originalPickUpPrice, "0"));
            productSkuReviewDTO.addField("dropShippingPrice", NumberUtil.toStr(beforeDropShippingPrice), NumberUtil.toStr(originalDropShippingPrice, "0"));
            productSkuReviewDTO.addField("unitPrice", NumberUtil.toStr(beforeUnitPrice), NumberUtil.toStr(originalUnitPrice, "0"));
            productSkuReviewDTO.addField("operationFee", NumberUtil.toStr(beforeOperationFee), NumberUtil.toStr(originalOperationFee, "0"));
            productSkuReviewDTO.addField("finalDeliveryFee", NumberUtil.toStr(beforeFinalDeliveryFee), NumberUtil.toStr(originalFinalDeliveryFee, "0"));
            newProductSkuReviewList.add(productSkuReviewDTO);
            productSku.setVerifyState(ProductVerifyStateEnum.Pending);
            skuShelfState = ShelfStateEnum.ForcedOffShelf;
        } else if (ProductVerifyStateEnum.Pending.equals(skuVerifyState)) {  // 正在审核中时，不接受新修改的价格
            skuShelfState = ShelfStateEnum.ForcedOffShelf;
        } else if (ProductVerifyStateEnum.Accepted.equals(skuVerifyState)) {  // 已通过审核时，需要提交价格变更审核
            if (!NumberUtil.equals(beforeMsrp, msrp)
                || !NumberUtil.equals(beforeDropShippingPrice, originalDropShippingPrice)
                || !NumberUtil.equals(beforePickUpPrice, originalPickUpPrice)) {
                // 记录变更字段及值
                ProductSkuReviewDTO productSkuReviewDTO = new ProductSkuReviewDTO();
                productSkuReviewDTO.setSiteId(siteId);
                productSkuReviewDTO.setProductSkuPriceId(productSkuPrice.getId());
                productSkuReviewDTO.setReviewType(PriceOperateLog.Update.name());
                productSkuReviewDTO.setProductSkuCode(productSkuCode);
                productSkuReviewDTO.addField("msrp", NumberUtil.toStr(beforeMsrp), NumberUtil.toStr(msrp, "0"));
                productSkuReviewDTO.addField("dropShippingPrice", NumberUtil.toStr(beforeDropShippingPrice), NumberUtil.toStr(originalDropShippingPrice, "0"));
                productSkuReviewDTO.addField("pickUpPrice", NumberUtil.toStr(beforePickUpPrice), NumberUtil.toStr(originalPickUpPrice, "0"));
                productSkuReviewDTO.addField("unitPrice", NumberUtil.toStr(beforeUnitPrice), NumberUtil.toStr(originalUnitPrice, "0"));
                productSkuReviewDTO.addField("operationFee", NumberUtil.toStr(beforeOperationFee), NumberUtil.toStr(originalOperationFee, "0"));
                productSkuReviewDTO.addField("finalDeliveryFee", NumberUtil.toStr(beforeFinalDeliveryFee), NumberUtil.toStr(originalFinalDeliveryFee, "0"));
                productSkuReviewDTO.addField("ruleId", StrUtil.toString(newPriceRuleId), StrUtil.toString(newPriceRuleId));
                productSkuReviewList.add(productSkuReviewDTO);
            }
        }

        if (ProductVerifyStateEnum.Accepted.equals(verifyState)) {
            if (CollUtil.isNotEmpty(productSkuReviewList)) {
                ProductReviewDTO productReviewDTO = new ProductReviewDTO();
                productReviewDTO.setProductCode(productCode);
                productReviewDTO.setSubmitTenantId(LoginHelper.getTenantId());
                productReviewDTO.setChangeFields(SPUChangeFields);
                productReviewDTO.setProductSkuReviewList(productSkuReviewList);
                productReviewDTO.setReviewType(ProductReviewTypeEnum.Price);
                productReviewDTO.setReviewStatus(ProductVerifyStateEnum.Pending);
                productReviewRecordService.generateReviewRecord(productReviewDTO);
            }

            if (CollUtil.isNotEmpty(newProductSkuReviewList)) {
                ProductReviewDTO productReviewDTO = new ProductReviewDTO();
                productReviewDTO.setProductCode(productCode);
                productReviewDTO.setSubmitTenantId(LoginHelper.getTenantId());
                productReviewDTO.setChangeFields(SPUChangeFields);
                productReviewDTO.setProductSkuReviewList(newProductSkuReviewList);
                productReviewDTO.setReviewType(ProductReviewTypeEnum.NewProductSku);
                productReviewDTO.setReviewStatus(ProductVerifyStateEnum.Pending);
                productReviewRecordService.generateReviewRecord(productReviewDTO);
            }
        }
        productSku.setShelfState(skuShelfState);
        iProductSkuService.saveOrUpdate(productSku);
    }

    /**
     * 对比价格
     *
     * @param oldPrice
     * @param newPrice
     * @return 全部相等返回true
     */
    private Boolean comparePrice(ProductSkuPrice oldPrice, ProductSkuPrice newPrice) {
        Boolean result;
        BigDecimal oldUnitPrice = oldPrice.getOriginalUnitPrice();
        BigDecimal oldOperationFee = oldPrice.getOriginalOperationFee();
        BigDecimal oldFinalDeliveryFee = oldPrice.getOriginalFinalDeliveryFee();
        BigDecimal oldMsrp = oldPrice.getMsrp();
        BigDecimal newUnitPrice = newPrice.getOriginalUnitPrice();
        BigDecimal newOperationFee = newPrice.getOriginalOperationFee();
        BigDecimal newFinalDeliveryFee = newPrice.getOriginalFinalDeliveryFee();
        BigDecimal newMsrp = newPrice.getMsrp();

        result = newUnitPrice.compareTo(oldUnitPrice) == 0 && newOperationFee.compareTo(oldOperationFee) == 0
            && newFinalDeliveryFee.compareTo(oldFinalDeliveryFee) == 0 && newMsrp.compareTo(oldMsrp) == 0;

        return result;
    }

    private <T> T attachmentConvertEntity(ProductAttachmentBo attachment, Class<T> attachmentEntity) {
        if (attachment == null) {
            return null;
        }

        String ossId = attachment.getOssId();
        if (ossId == null) {
            return null;
        }

        String attachmentName = attachment.getAttachmentName();
        String attachmentShowUrl = attachment.getAttachmentShowUrl();
        String attachmentSavePath = attachment.getAttachmentSavePath();
        String attachmentSort = attachment.getAttachmentSort();
        String attachmentType = attachment.getAttachmentType();
        String suffix = FileUtil.getSuffix(attachmentName);
        Long ossIdL = NumberUtil.parseLong(ossId);

        T newInstance = ReflectUtil.newInstance(attachmentEntity);

        ReflectUtil.setFieldValue(newInstance, "ossId", ossIdL);
        ReflectUtil.setFieldValue(newInstance, "attachmentName", attachmentName);
        ReflectUtil.setFieldValue(newInstance, "attachmentOriginalName", attachmentName);
        ReflectUtil.setFieldValue(newInstance, "attachmentShowUrl", attachmentShowUrl);
        ReflectUtil.setFieldValue(newInstance, "attachmentSavePath", attachmentSavePath);
        ReflectUtil.setFieldValue(newInstance, "attachmentSuffix", suffix);
        ReflectUtil.setFieldValue(newInstance, "attachmentType", attachmentType);
        ReflectUtil.setFieldValue(newInstance, "attachmentSort", attachmentSort);
        return newInstance;
    }

    private List<ProductSkuAttachment> downloadImage(List<String> images) {
        // 处理图片
        List<ProductSkuAttachment> skuAttachmentList = new ArrayList<>();
        for (int i = 0; i < images.size(); i++) {
            String image = images.get(i);
            File file = null;
            try {
                String suffix = FileUtil.getSuffix(image);
                // 检查是否是非法的后缀
                if (StrUtil.length(suffix) > 5) {
                    continue;
                }

                String tempSavePath = fileProperties.getTempSavePath();
                String fileName = UUID.fastUUID().toString(true) + "." + suffix;
                String tempPath = tempSavePath + File.separator + "productImport" + File.separator + fileName;
                file = FileUtil.newFile(tempPath);
                HttpUtil.downloadFile(image, file, 5000);
                BufferedInputStream inputStream = FileUtil.getInputStream(file);
                SysOssVo sysOssVo = SystemEventUtils.uploadFile(inputStream, fileName);

                ProductSkuAttachment productSkuAttachment = new ProductSkuAttachment();
                productSkuAttachment.setOssId(sysOssVo.getOssId());
                productSkuAttachment.setAttachmentName(fileName);
                productSkuAttachment.setAttachmentOriginalName(fileName);
                productSkuAttachment.setAttachmentShowUrl(sysOssVo.getUrl());
                productSkuAttachment.setAttachmentSavePath(sysOssVo.getSavePath());
                productSkuAttachment.setAttachmentSuffix(suffix);
                productSkuAttachment.setAttachmentSort(i);
                productSkuAttachment.setAttachmentType(AttachmentTypeEnum.Image);
                skuAttachmentList.add(productSkuAttachment);
            } catch (Exception e) {
                log.error("下载图片失败 image = {}，原因 {}", image, e.getMessage(), e);
            } finally {
                FileUtil.del(file);
            }
        }
        return skuAttachmentList;
    }

    /**
     * 查询仓库Map
     *
     * @return
     */
    private Map<String, List<WarehouseTempVo>> queryWarehouseMap() {
        Map<String, List<WarehouseTempVo>> warehouseMap = RedisUtils.getCacheMap(RedisConstants.ZSMALL_SUPPLIER_WAREHOUSE);
        if (CollUtil.isEmpty(warehouseMap)) {
            warehouseMap = new HashMap<>();
            for (WarehouseTypeEnum warehouseType : WarehouseTypeEnum.values()) {
                LambdaQueryWrapper<Warehouse> lqw = Wrappers.lambdaQuery();
                lqw.eq(Warehouse::getWarehouseType, warehouseType);
                lqw.eq(Warehouse::getWarehouseState, GlobalStateEnum.Valid);
                List<Warehouse> warehouses = iWarehouseService.list(lqw);

                List<WarehouseTempVo> voList = BeanUtil.copyToList(warehouses, WarehouseTempVo.class);
                for (WarehouseTempVo warehouseTempVo : voList) {
                    List<LogisticsTemplate> templateList = iLogisticsTemplateService.queryByWarehouse(warehouseTempVo.getWarehouseSystemCode());
                    if (CollUtil.isNotEmpty(templateList)) {
                        List<JSONObject> templateSelectVoList = new ArrayList<>();
                        templateList.forEach(template -> {
                            JSONObject entries = new JSONObject();
                            entries.set("templateName", template.getTemplateName());
                            entries.set("logisticsTemplateNo", template.getLogisticsTemplateNo());
                            templateSelectVoList.add(entries);
                        });
                        warehouseTempVo.setLogisticsTemplateSelect(templateSelectVoList);
                    }
                }
                warehouseMap.put(warehouseType.name(), voList);
            }
            RedisUtils.setCacheMap(RedisConstants.ZSMALL_SUPPLIER_WAREHOUSE, warehouseMap);
            RedisUtils.expireCacheMap(RedisConstants.ZSMALL_SUPPLIER_WAREHOUSE, Duration.ofHours(2));
        }
        return warehouseMap;
    }

    private Map<String, List<WarehouseTempVo>> queryWarehouseMapNoRedis() {
//        Map<String, List<WarehouseTempVo>> warehouseMap = RedisUtils.getCacheMap(RedisConstants.ZSMALL_SUPPLIER_WAREHOUSE);
//        if (CollUtil.isEmpty(warehouseMap)) {
            Map<String, List<WarehouseTempVo>> warehouseMap = new HashMap<>();
            for (WarehouseTypeEnum warehouseType : WarehouseTypeEnum.values()) {
                LambdaQueryWrapper<Warehouse> lqw = Wrappers.lambdaQuery();
                lqw.eq(Warehouse::getWarehouseType, warehouseType);
                lqw.eq(Warehouse::getWarehouseState, GlobalStateEnum.Valid);
                lqw.orderByDesc(Warehouse::getCreateTime).orderByDesc(Warehouse::getId);
                List<Warehouse> warehouses = iWarehouseService.list(lqw);

                List<WarehouseTempVo> voList = BeanUtil.copyToList(warehouses, WarehouseTempVo.class);
//                for (WarehouseTempVo warehouseTempVo : voList) {
//                    List<LogisticsTemplate> templateList = iLogisticsTemplateService.queryByWarehouse(warehouseTempVo.getWarehouseSystemCode());
//                    if (CollUtil.isNotEmpty(templateList)) {
//                        List<JSONObject> templateSelectVoList = new ArrayList<>();
//                        templateList.forEach(template -> {
//                            JSONObject entries = new JSONObject();
//                            entries.set("templateName", template.getTemplateName());
//                            entries.set("logisticsTemplateNo", template.getLogisticsTemplateNo());
//                            templateSelectVoList.add(entries);
//                        });
//                        warehouseTempVo.setLogisticsTemplateSelect(templateSelectVoList);
//                    }
//                }
                warehouseMap.put(warehouseType.name(), voList);
            }
//            RedisUtils.setCacheMap(RedisConstants.ZSMALL_SUPPLIER_WAREHOUSE, warehouseMap);
//            RedisUtils.expireCacheMap(RedisConstants.ZSMALL_SUPPLIER_WAREHOUSE, Duration.ofHours(2));
//        }
        return warehouseMap;
    }

    /**
     * 将字符串转换为指定格式,将产品的属性值转化格式
     *
     * @param input
     * @return
     */
    public static String transformString(String input) {
        if(StringUtils.isNotEmpty(input)){
            // 使用分号分割字符串
            String[] parts = input.split(";");

            StringBuilder result = new StringBuilder();
            for (String part : parts) {
                // 去除末尾的分号（如果有）
                if (part.endsWith(";")) {
                    part = part.substring(0, part.length() - 1);
                }
                // 替换 '-' 为 ':'
                String transformedPart = part.replace("-", ":");
                result.append(transformedPart).append("\n");
            }
            // 移除最后一个换行符
            if (result.length() > 0) {
                result.setLength(result.length() - 1);
            }
            return result.toString();
        }
        return null;
    }

    /**
     * 产品sku列表赋值库存信息
     * @param productSkuLists
     */
    public void stockAssignment(List<ProductSkuListVo> productSkuLists){
        if(CollUtil.isEmpty(productSkuLists)){
            return;
        }
        List<String> productSkuCodeList = productSkuLists.stream().map(ProductSkuListVo::getProductSkuCode).distinct().collect(Collectors.toList());
        if(CollUtil.isEmpty(productSkuCodeList)){
            return;
        }
        // 根据productSkuCode获取库存信息
        List<ProductSkuStock> productSkuStockList = iProductSkuStockService.listByProductSkuCodes(productSkuCodeList);
        if(CollUtil.isEmpty(productSkuStockList)){
            return;
        }
        Map<String,Integer> productSkuStockMap = new HashMap<>();
        Map<String,Integer> productSkuPickupLockUsedStockMap = new HashMap<>();
        Map<String,Integer> productSkuDropShippingLockUsedStockMap = new HashMap<>();
        // 库存异常标识
        Map<String,Integer> productSkuLockExceptionCodeMap = new HashMap<>();
        for(String productSkuCode : productSkuCodeList){
            Integer stock = 0;
            Integer pickupLockUsed = 0;
            Integer dropShippingLockUsed = 0;
            Integer lockExceptionCode = 0;
            for(ProductSkuStock productSkuStock : productSkuStockList){
                if(productSkuCode.equals(productSkuStock.getProductSkuCode())){
                    if(null != productSkuStock.getDropShippingStockAvailable() && productSkuStock.getDropShippingStockAvailable().equals(1)){
                        stock+=productSkuStock.getStockAvailable();
                    }
                    pickupLockUsed += productSkuStock.getPickupLockUsed() + productSkuStock.getPickupLockReserved();
                    dropShippingLockUsed += productSkuStock.getDropShippingLockUsed() + productSkuStock.getDropShippingLockReserved();
                    if(null != productSkuStock.getLockExceptionCode() && productSkuStock.getLockExceptionCode() != 0){
                        lockExceptionCode = productSkuStock.getLockExceptionCode();
                    }
                }
            }
            productSkuLockExceptionCodeMap.put(productSkuCode,lockExceptionCode);
            productSkuStockMap.put(productSkuCode,stock);
            productSkuPickupLockUsedStockMap.put(productSkuCode,pickupLockUsed);
            productSkuDropShippingLockUsedStockMap.put(productSkuCode,dropShippingLockUsed);
        }
        // 赋值库存信息
        productSkuLists.forEach(productSkuListVo -> {
            Integer stock = productSkuStockMap.get(productSkuListVo.getProductSkuCode());
            productSkuListVo.setProxyStockTotal(stock);
            productSkuListVo.setPickupLockUsed(productSkuPickupLockUsedStockMap.get(productSkuListVo.getProductSkuCode()));
            productSkuListVo.setDropShippingLockUsed(productSkuDropShippingLockUsedStockMap.get(productSkuListVo.getProductSkuCode()));
            productSkuListVo.setLockExceptionCode(productSkuLockExceptionCodeMap.get(productSkuListVo.getProductSkuCode()));
        });
    }

    @Override
    public void synchronizationProductToEs(String productCode) {
        List<Product> products;
        if (StrUtil.isNotEmpty(productCode)){
            String[] split = productCode.split(",");
            LambdaQueryWrapper<Product> q = new LambdaQueryWrapper<>();
            q.in(Product::getProductCode,Set.of(split));
            products = TenantHelper.ignore(() -> iProductService.getBaseMapper().selectList(q));
        }else {
            products = TenantHelper.ignore(() -> iProductService.getBaseMapper().selectList());
        }
        products.forEach(esProductSupport::productUpload);
    }

    @Override
    public void productListExportNew(ProductQueryBo bo, HttpServletResponse response) {
        String fileName = StrUtil.format(FileNameConstants.PRODUCT_LIST_EXPORT, DateUtil.format(new Date(), "yyMMdd-HHmmssSS"));
        Locale headerLocale = ServletUtils.getHeaderLocale();
        DownloadRecordUtil.generate(fileName, DownloadTypePlusEnum.ProductListExport, tempFileSavePath -> {
            try {
                TimeInterval timer = DateUtil.timer();
                File tempFile = new File(tempFileSavePath);
                BufferedOutputStream outputStream = FileUtil.getOutputStream(tempFile);
                List<ProductExportDto> productExportDtos = writeProductExportV2(bo, bo.getTenantId());
                com.hengjian.common.excel.utils.ExcelUtil.exportExcelWithLocale(productExportDtos, "Product list", ProductExportDto.class, false, outputStream, headerLocale);
                IoUtil.close(outputStream);
                Console.log("[商品导出]耗时: {} ms", timer.intervalMs());
                return tempFile;
            } catch (Exception e) {
                log.error(StrUtil.format(StrUtil.format("[商品导出],处理数据异常，原因：", e)));
                throw new RuntimeException(e);
            }
        });
    }
}
