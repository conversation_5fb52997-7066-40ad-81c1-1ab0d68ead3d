package com.zsmall.activity.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.utils.SpringUtils;
import com.hengjian.common.log.annotation.InMethodLog;
import com.zsmall.activity.biz.service.IStorageFeeService;
import com.zsmall.activity.biz.support.StorageFeeSupport;
import com.zsmall.activity.entity.domain.StorageFeeInfo;
import com.zsmall.activity.entity.domain.bo.storageFee.StorageFeePayBo;
import com.zsmall.activity.entity.domain.dto.productActivity.DistributorProductActivity;
import com.zsmall.activity.entity.domain.dto.productActivity.SupplierProductActivity;
import com.zsmall.activity.entity.iservice.IDistributorProductActivityService;
import com.zsmall.activity.entity.iservice.IStorageFeeInfoService;
import com.zsmall.activity.entity.iservice.ISupplierProductActivityService;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.common.enums.storageFee.FeeStateEnum;
import com.zsmall.system.entity.domain.event.CheckPaymentPasswordEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025年7月9日  16:14
 * @description: 仓储费实现类
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class StorageFeeServiceImpl implements IStorageFeeService {

    private final StorageFeeSupport storageFeeSupport;
    private final IStorageFeeInfoService storageFeeInfoService;
    private final IDistributorProductActivityService distributorProductActivityService;
    private final ISupplierProductActivityService supplierProductActivityService;

    @InMethodLog("仓储费支付方法")
    @Transactional(rollbackFor = Exception.class)
    public R<Void> payStorageFee (StorageFeePayBo storageFeePayBo) throws Exception {
        // 校验支付密码
        SpringUtils.context().publishEvent(new CheckPaymentPasswordEvent(storageFeePayBo.getPaymentPassword()));
        // 校验仓储费状态,判断仓储费的状态是否为确认中
        List<StorageFeeInfo> storageFeeInfoList = storageFeeInfoService.list(new LambdaQueryWrapper<StorageFeeInfo>().in(StorageFeeInfo::getStorageFeeId, storageFeePayBo.getStorageFeeIds()));
        if(CollUtil.isEmpty(storageFeeInfoList)){
            log.info("仓储费不存在");
            return R.fail(ZSMallStatusCodeEnum.STORAGE_FEE_NOT_EXIST.args(storageFeePayBo.getStorageFeeIds()));
        }
        // 错误状态的仓储费ID
        List<String> errorStorageFeeIdList = new ArrayList<>();
        for (StorageFeeInfo storageFeeInfo : storageFeeInfoList){
            if(!FeeStateEnum.CONFIRMING.getValue().equals(storageFeeInfo.getFeeState())){
                errorStorageFeeIdList.add(storageFeeInfo.getStorageFeeId());
            }
        }
        if(CollUtil.isNotEmpty(errorStorageFeeIdList)){
            return R.fail(ZSMallStatusCodeEnum.STORAGE_FEE_STATE_ERROR.args(errorStorageFeeIdList));
        }
        // 钱包支付
        storageFeeSupport.storageFeePay(storageFeeInfoList);
        // 修改仓储费状态为已确认
        storageFeeInfoList.stream()
                          .forEach(storageFeeInfo -> storageFeeInfo.setFeeState(FeeStateEnum.CONFIRMED.getValue()));
        storageFeeInfoService.updateBatchById(storageFeeInfoList);
        // 给活动相关表赋值仓储费
        List<String> activityIdList = storageFeeInfoList.stream()
                                                    .map(StorageFeeInfo::getActivityId)
                                                    .distinct()
                                                    .collect(Collectors.toList());
        if(CollUtil.isEmpty(activityIdList)){
            log.info("仓储费的活动ID不存在");
            return R.fail(ZSMallStatusCodeEnum.STORAGE_FEE_NOT_EXIST.args(storageFeePayBo.getStorageFeeIds()));
        }
        List<DistributorProductActivity> distributorProductActivityList = distributorProductActivityService.list(new LambdaQueryWrapper<DistributorProductActivity>().in(DistributorProductActivity::getDistributorActivityCode, activityIdList));
        if(CollUtil.isEmpty(distributorProductActivityList)){
            log.info("分销商仓储费活动不存在");
            return R.fail(ZSMallStatusCodeEnum.STORAGE_FEE_NOT_EXIST.args(storageFeePayBo.getStorageFeeIds()));
        }
        // 分销商活动已支付仓储费赋值
        for (StorageFeeInfo storageFeeInfo : storageFeeInfoList){
            for (DistributorProductActivity distributorProductActivity : distributorProductActivityList){
                if(storageFeeInfo.getActivityId().equals(distributorProductActivity.getDistributorActivityCode())){
                    distributorProductActivity.setStorageFeePaidTotal(distributorProductActivity.getStorageFeePaidTotal().add(storageFeeInfo.getTotalStorageFee()));
                }
            }
        }
        List<Long> supplierActivityIdList = distributorProductActivityList.stream()
                                                                        .map(DistributorProductActivity::getSupplierActivityId)
                                                                        .distinct()
                                                                        .collect(Collectors.toList());
        if(CollUtil.isEmpty(supplierActivityIdList)){
            log.info("供应商活动Id不存在");
            return R.fail(ZSMallStatusCodeEnum.STORAGE_FEE_NOT_EXIST.args(storageFeePayBo.getStorageFeeIds()));
        }
        List<SupplierProductActivity> supplierProductActivityList = supplierProductActivityService.list(new LambdaQueryWrapper<SupplierProductActivity>().in(SupplierProductActivity::getId, supplierActivityIdList));
        if(CollUtil.isEmpty(supplierProductActivityList)){
            log.info("供应商活动不存在");
            return R.fail(ZSMallStatusCodeEnum.ACTIVITY_NOT_EXIST.args(storageFeePayBo.getStorageFeeIds()));
        }
        // 供应商活动已支付仓储费赋值
        for (SupplierProductActivity supplierProductActivity : supplierProductActivityList) {
            for (DistributorProductActivity distributorProductActivity : distributorProductActivityList){
                if(supplierProductActivity.getId().equals(distributorProductActivity.getSupplierActivityId())){
                    if(null == supplierProductActivity.getStorageFeePaidTotal()){
                        supplierProductActivity.setStorageFeePaidTotal(distributorProductActivity.getStorageFeePaidTotal());
                    }else {
                        supplierProductActivity.setStorageFeePaidTotal(supplierProductActivity.getStorageFeePaidTotal().add(distributorProductActivity.getStorageFeePaidTotal()));
                    }
                }
            }
        }
        supplierProductActivityService.updateBatchById(supplierProductActivityList);
        return R.ok();
    }
}
